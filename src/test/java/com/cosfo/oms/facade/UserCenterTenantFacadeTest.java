package com.cosfo.oms.facade;

import com.cosfo.oms.common.context.SortTypeEnum;
import com.cosfo.oms.facade.dto.tenant.TenantAddDTO;
import com.cosfo.oms.facade.dto.tenant.TenantInputQueryDTO;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.usercenter.client.tenant.resp.TenantAndBusinessInfoResultResp;
import net.xianmu.usercenter.client.tenant.resp.TenantResultResp;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@SpringBootTest
class UserCenterTenantFacadeTest {

    @Resource
    private UserCenterTenantFacade userCenterTenantFacade;

    @Test
    void getTenantsByIds() {
        List<TenantResultResp> tenantsByIds = userCenterTenantFacade.getTenantsByIds(Lists.newArrayList(2L));
        System.out.println(tenantsByIds);
    }

    @Test
    void getTenantById() {
        TenantResultResp tenantById = userCenterTenantFacade.getTenantById(2L);
        System.out.println(tenantById);
    }

    @Test
    void getTenantAndCompanyById() {
        TenantAndBusinessInfoResultResp tenantAndCompanyById = userCenterTenantFacade.getTenantAndCompanyById(2L);
        System.out.println(tenantAndCompanyById);
    }

    @Test
    void getTenantAndCompanyByQuery() {
        List<TenantAndBusinessInfoResultResp> tenantAndCompanyByQuery = userCenterTenantFacade.getTenantAndCompanyByQuery(TenantInputQueryDTO.builder().build());
        System.out.println(tenantAndCompanyByQuery);
    }

    @Test
    void getTenantAndCompanyPage() {
        PageInfo<TenantAndBusinessInfoResultResp> tenantAndCompanyPage = userCenterTenantFacade.getTenantAndCompanyPage(TenantInputQueryDTO.builder().tenantName("测试").pageIndex(1).pageSize(10).build());
        System.out.println(tenantAndCompanyPage);
    }

    @Test
    void getTenantAndCompanyByIds() {
        List<TenantAndBusinessInfoResultResp> tenantAndCompanyByIds = userCenterTenantFacade.getTenantAndCompanyByIds(Lists.newArrayList(2L));
        System.out.println(tenantAndCompanyByIds);
    }

    @Test
    void getTenantsByQuery() {
        List<TenantResultResp> tenantsByQuery = userCenterTenantFacade.getTenantsByQuery(TenantInputQueryDTO.builder().type(1).build());
        System.out.println(tenantsByQuery);
    }

    @Test
    void addTenantInfo() {
        TenantAddDTO tenantAddDTO = new TenantAddDTO();
        tenantAddDTO.setSystemOrigin(SystemOriginEnum.COSFO_MANAGE.getType());
        tenantAddDTO.setLoginPhone("***********");
        tenantAddDTO.setAdminId(13145L);
        tenantAddDTO.setCompanyName("不吃香菜123");
        tenantAddDTO.setMerchantName("不吃香菜商城名称");
        tenantAddDTO.setCreditCode("123");
        tenantAddDTO.setProvince("浙江省");
        tenantAddDTO.setCity("杭州市");
        tenantAddDTO.setArea("余杭区");
        tenantAddDTO.setAddress("到底是那里");
        tenantAddDTO.setCompanyPhone("***********");
        tenantAddDTO.setCompanyAreaPhone("86");
        tenantAddDTO.setOpAuthUserId(10055L);
        userCenterTenantFacade.addTenantInfo(tenantAddDTO);
    }

    @Test
    void updateTenantInfo() {
    }

    @Test
    void getTenantAndCompanyPageSortTest() {
        TenantInputQueryDTO.builder().tenantName("测试").pageIndex(1).pageSize(10).sortType(SortTypeEnum.DESCENDING.getType()).sortWord("FIELD(`id`, 5, 3, 7, 1)").build();
//        PageInfo<TenantAndBusinessInfoResultResp> tenantAndCompanyPage = userCenterTenantFacade.getTenantAndCompanyPage();

//        System.out.println(tenantAndCompanyPage);
    }
}