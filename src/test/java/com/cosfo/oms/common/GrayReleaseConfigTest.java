package com.cosfo.oms.common;

import com.cosfo.oms.common.config.GrayReleaseConfig;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * @author: monna.chen
 * @Date: 2023/9/21 16:27
 * @Description:
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class GrayReleaseConfigTest {
    @Resource
    private GrayReleaseConfig grayReleaseConfig;

    @Test
    public void getGrayConfig() {
        log.info(">>>目前货品中心灰度开关是：" + grayReleaseConfig.isGoodsCenterGrayRelease());
    }

}
