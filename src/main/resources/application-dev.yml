server:
  port: 8081
# 数据库配置
spring:
  application:
    name: cosfo-oms
  datasource:
    hikari:
      minimum-idle: 3
      maximum-pool-size: 10
      max-lifetime: 30000   #不能小于30秒，否则默认回到1800秒
      connection-test-query: SELECT 1
    username: test
    password: xianmu619
    #?serverTimezone=UTC解决时区的报错
    url: ********************************************************************************************************************************
    # mysql5 的驱动是 com.mysql.jdbc.Driver, mysql8的驱动是 com.mysql.cj.jdbc.Driver
    driver-class-name: com.mysql.cj.jdbc.Driver
    # 自定义数据源
    type: com.alibaba.druid.pool.DruidDataSource
    #druid 数据源专有配置
    initialSize: 5
    minIdle: 5
    maxActive: 80
    maxWait: 60000
    timeBetweenEvictionRunsMillis: 60000
    minEvictableIdleTimeMillis: 300000
  # redis配置
  redis:
    host: test-redis.summerfarm.net
    password: xianmu619
    port: 6379
    timeout: 6000
    database: 0
  # auth服务依赖
  authRedis:
    host: test-redis.summerfarm.net
    port: 6379
    password: xianmu619
    timeout: 5000 # 连接超时时间（毫秒）
    database: 0
    jedis:
      pool:
        max-active: 5
        max-idle: 5
        min-idle: 5
        max-wait: 5
  schedulerx2:
    endpoint: acm.aliyun.com
    namespace: 0fba89cd-351e-4e9f-86dc-4b4fbc06170e
    groupId: saas-oms
    appKey: NRgEbOWyOk8BoKUpr18CCg==

summerfarm:
  api-host: http://manage-svc
saasmall:
  api-host: http://cosfo-mall-svc


rocketmq:
  name-server: test-mq-nameserver.summerfarm.net:9876
  producer:
    enable-msg-trace: off
    group: GID_oms
    send-message-timeout: 10000
    access-key: RocketmqAdmin
    secret-key: RocketmqAdmin
  consumer:
    access-key: RocketmqAdmin
    secret-key: RocketmqAdmin
# 日志文件路径
log-path: ${APP_LOG_DIR:./log}
tenant:
  ftTenantId: 0
  xmTenantId: 1
logging:
  level:
    root: info
    org.springframework: INFO
    org.mybatis: INFO
    com.cosfo.oms: INFO
  pattern:
    console: "%d - %msg%n"
pagehelper:
  helperDialect: mysql
  reasonable: false  # 禁用合理化时，如果pageNum<1或pageNum>pages会返回空数据
  supportMethodsArguments: true
  params: count=countSql
root:
  pageSize: 10
# 七牛云配置
qiniu:
  ACCESS_KEY: D7qgaXDWTIv0ormcO8IozO1RyT7P1YLP-y3tObIJ
  SECRET_KEY: ZLVTkrc7cwZlyNhLVieyeBwJ-UD_0p4Co9ZdSu3T
  bucketname: cosfo
jindie:
  suge:
    summerfarmNo: 777
    warehouseNo: CK001
    supplierId: VEN00005

dubbo:
  application:
    name: ${spring.application.name}
    id: ${spring.application.name}
  registry:
    protocol: nacos
    address: nacos://test-nacos.summerfarm.net:11000
    #address: nacos://********:11000
    # address: nacos://127.0.0.1:11000
    #    address: nacos://*********:11000
    parameters:
      namespace: a9f94e14-0f25-4567-a038-b32e83829046
  protocol:
    id: dubbo
    name: dubbo
    port: 20880
  consumer:
    version: 1.0.0
    group: online
    retries: 0
    timeout: 10000
    check: false
  provider:
    version: 1.0.0
    group: online
    timeout: 6000
    retries: 0
    telnet: ls,ps,cd,pwd,trace,count,invoke,select,status,log,help,clear,exit,shutdown
### 配置第三方平台的appId
tp:
  appId: wx85a9fed1e711916e

# 汇付配置
huifu:
  sys_id: 6666000124683186
  product_id: PAYUN
  channel_no: 00005016
  private_key: MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAIkLjvPSkZhis7K3oFStvyrs0UXEADzLeDD5rb5j7VAVzeRA3Fx0kMBqtD+WEnqxGi0H3tM+mGRSNuc/ImF+3Iz9xAuwJCOS5g9ZDwQvwmvG9YrzDvcK1u957U53ukT3BHYt+WXZ46/Iyqsq7pSgLo/EzbMf8IfuqZqIKWHsx1YFAgMBAAECgYAMD8BdJUM7LjSynga2bTROCtnAUifTMfU6Gj94akMQsVqVpD/Aw2GaDcofbo3hzoSHQhISdYfkDHhYke3stsWiYgoDK2Cqow3BtocGSGePwFprJXWQJfKBO1ADb4zEka6q3zo9lcxsCqa+fx1G3uLIJNin3QWqOLXquo0GXOgEAQJBAMd/WNIeqKi/MNv5MtpiyIlGxOmdH7NPn7oW0exEzFeWsPLsl+******************************+XXhKmUCQQCv3BRFLKAH9aivHcVJZaqzd5VmFaGSWeZkiBLBa1i8ZneQv4rc1/p8M5Rvg2WHY+JTx7KxqygyahN3teqvx/MhAkEAruCSErbvb+URRnL/QfLACZ4wtPyYMk4FHVItuKhiXBFrkbcWOE/P0ashKEWsmZp45ufvviglR08LGbEJe2zjBQJALyQvyttLitavgUHZwPMf7zv/MH5b8X9n40sWvAKqptZQ9txhvRGoc+Lfx4TRkpmT8iF2JWpcPCdzUIPThYt0AQJAVIJZBYLsGAcmdhs22OCL0J62FVoxcYFBAKXA2en33goOpF/idpWUo3w++eZGAFqdLh5TKyJCj5xOgkVuYFuUAA==
xm:
  log:
    enable: true
    resp: true
  oss:
    persistent-storage:
      bucketName: test-app-perm
      endpoint: oss-cn-hangzhou.aliyuncs.com
      accessKeyId: LTAI5tHzxfnRMRvimPVojjU5
      accessKeySecret: ******************************
    temporary-storage:
      bucketName: test-app-temp
      endpoint: oss-cn-hangzhou.aliyuncs.com
      accessKeyId: LTAI5tHzxfnRMRvimPVojjU5
      accessKeySecret: ******************************
  downloadcenter:
    consumer_group_suffix: cosfo-oms
    # tag前缀为tag_
    consumer_tag_suffix: cosfo-oms
H5:
  mall:
    url: https://devmall.cosfo.cn/index.html#/pages/loading/index?token=


nacos:
  config:
    server-addr: test-nacos.summerfarm.net:11000
    namespace: 19b82444-16f9-4d22-a522-b7ac6495c954