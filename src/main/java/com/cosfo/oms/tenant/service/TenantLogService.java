package com.cosfo.oms.tenant.service;

import com.cosfo.oms.tenant.model.dto.PrivilegesFunctionSetDTO;
import com.cosfo.oms.tenant.model.po.TenantPrivilegesConfig;
import net.xianmu.log.annation.BizLogRecord;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface TenantLogService {

    void logVersionUpdate(Long tenantId, Integer oldVersion, Integer newVersion);

    void logVersionExpireDateUpdate(Long tenantId, LocalDate oldExpireDate, LocalDate newExpireDate);


    void logPrivileges(Long tenantId, List<TenantPrivilegesConfig> oldConfig, List<PrivilegesFunctionSetDTO> functionSetList);
}
