package com.cosfo.oms.tenant.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.oms.common.context.tenant.TenantConfigEnum;
import com.cosfo.oms.tenant.model.dto.TenantQueryDTO;
import com.cosfo.oms.tenant.model.po.TenantCommonConfig;

import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2024-02-21
 * @Description:
 */
public interface TenantCommonConfigService {


    /**
     * 分页查询租户配置
     * @param tenantQueryDTO
     * @param tenantConfigEnum
     * @param tenantIds
     * @return
     */
    Page<TenantCommonConfig> pageQueryCommonConfig(TenantQueryDTO tenantQueryDTO, TenantConfigEnum tenantConfigEnum, List<Long> tenantIds);

    boolean initTenantConfig(Long tenantId, TenantConfigEnum saveWorry);
}
