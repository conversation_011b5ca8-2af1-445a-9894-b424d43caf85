package com.cosfo.oms.tenant.service;

import com.cosfo.oms.tenant.model.dto.FunctionSetDTO;
import com.cosfo.oms.tenant.model.dto.FunctionSetQueryDTO;
import com.github.pagehelper.PageInfo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface TenantFunctionSetService {

    /**
     * 获取分页列表
     * @param queryDTO
     * @return
     */
    PageInfo<FunctionSetDTO> page(FunctionSetQueryDTO queryDTO);

    /**
     * 获取列表
     */
    List<FunctionSetDTO> list(FunctionSetQueryDTO queryDTO);

    /**
     * 获取详情
     * @param id
     * @return
     */
    FunctionSetDTO detail(Long id);

    /**
     * 新增
     * @param functionSetDTO
     * @return
     */
    Boolean add(FunctionSetDTO functionSetDTO);

    /**
     * 修改
     * @param functionSetDTO
     * @return
     */
    Boolean update(FunctionSetDTO functionSetDTO);

    /**
     * 删除
     * @param id
     * @return
     */
    Boolean delete(Long id);

    /**
     * 获取销售版本对应的功能集
     * @param id
     * @return
     */
    FunctionSetDTO getFunctionSetBySaleVersion(Integer saleVersion);

    /**
     * 根据id批量查询
     */
    List<FunctionSetDTO> listByIds(List<Long> ids);

    /**
     * 根据id批量查询
     */
    Map<Long, FunctionSetDTO> mapByIds(List<Long> ids);

}
