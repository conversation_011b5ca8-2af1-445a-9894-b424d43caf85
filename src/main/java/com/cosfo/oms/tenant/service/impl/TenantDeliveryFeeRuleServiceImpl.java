package com.cosfo.oms.tenant.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.cosfo.oms.common.constant.NumberConstants;
import com.cosfo.oms.common.context.TenantDeliveryFeeRuleEnums;
import com.cosfo.oms.common.context.TenantTypeEnum;
import com.cosfo.oms.common.exception.DefaultServiceException;
import com.cosfo.oms.common.result.ResultDTO;
import com.cosfo.oms.common.result.ResultDTOEnum;
import com.cosfo.oms.common.utils.AssertParam;
import com.cosfo.oms.common.utils.PageInfoHelper;
import com.cosfo.oms.model.dto.LoginContextInfoDTO;
import com.cosfo.oms.tenant.convert.TenantDeliveryFeeAreaConvert;
import com.cosfo.oms.tenant.dao.TenantDeliveryFeeAreaDao;
import com.cosfo.oms.tenant.event.TenantChangeEvent;
import com.cosfo.oms.tenant.mapper.TenantDeliveryFeeRuleMapper;
import com.cosfo.oms.tenant.model.dto.*;
import com.cosfo.oms.tenant.model.po.TenantDeliveryFeeArea;
import com.cosfo.oms.tenant.model.po.TenantDeliveryFeeRule;
import com.cosfo.oms.tenant.model.vo.TenantDeliveryFeeAreaRuleVO;
import com.cosfo.oms.tenant.model.vo.TenantDeliveryFeeAreaVO;
import com.cosfo.oms.tenant.model.vo.TenantVO;
import com.cosfo.oms.tenant.service.TenantDeliveryFeeRuleService;
import com.cosfo.oms.tenant.service.TenantService;
import com.github.pagehelper.PageHelper;
import net.xianmu.common.result.CommonResult;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2022/7/27 11:16
 */
@Service
public class TenantDeliveryFeeRuleServiceImpl implements TenantDeliveryFeeRuleService {

    @Resource
    private TenantDeliveryFeeRuleMapper tenantDeliveryFeeRuleMapper;

    @Resource
    private TenantService tenantService;

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;
    @Resource
    private TenantDeliveryFeeAreaDao tenantDeliveryFeeAreaDao;

    @Override
    public ResultDTO listAll(Integer pageIndex, Integer pageSize, TenantDeliveryFeeRuleDTO ruleDTO) {
        PageHelper.startPage(pageIndex, pageSize);
        List<TenantDeliveryFeeRuleDTO> ruleList = tenantDeliveryFeeRuleMapper.listAll(ruleDTO);
        return ResultDTO.success(PageInfoHelper.createPageInfo(ruleList, pageSize));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public CommonResult<Boolean> updateDeliveryFeeRule(TenantDeliveryFeeRuleDTO ruleDTO, LoginContextInfoDTO loginContextInfoDTO) {
        // 校验数据
        checkDeliveryFeeRule(ruleDTO);

        Long tenantId = ruleDTO.getTenantId();
        TenantDeliveryFeeRuleDTO feeRule = tenantDeliveryFeeRuleMapper.getDetailByTenantId(ruleDTO.getTenantId());

        TenantDeliveryFeeRule rule = new TenantDeliveryFeeRule();
        rule.setOperator(loginContextInfoDTO.getUserName());

        if (!ObjectUtils.isEmpty(feeRule)) {
            BeanUtils.copyProperties(ruleDTO, rule);
            rule.setId(feeRule.getId());
            rule.setOperator(loginContextInfoDTO.getUserName());
            rule.setDefaultPrice(ruleDTO.getDefaultPrice());
            rule.setFreeNumber(ruleDTO.getFreeNumber());
            rule.setFreeType(ruleDTO.getFreeType());
            tenantDeliveryFeeRuleMapper.updateByPrimaryKeySelective(rule);
        } else {
            rule.setTenantId(ruleDTO.getTenantId());
            rule.setType(ruleDTO.getType());
            rule.setDefaultPrice(ruleDTO.getDefaultPrice());
            rule.setFreeNumber(ruleDTO.getFreeNumber());
            rule.setFreeType(ruleDTO.getFreeType());
            tenantDeliveryFeeRuleMapper.insertSelective(rule);
        }

        // 查询最大groupId
        Integer maxGroupId = tenantDeliveryFeeAreaDao.queryMaxGroupId(tenantId, rule.getId());
        // 删除所有区域规则
        tenantDeliveryFeeAreaDao.delete(tenantId, rule.getId());
        // 修改运费区域规则
        List<TenantDeliveryFeeAreaRuleVO> tenantDeliveryFeeAreaRuleVOS = ruleDTO.getTenantDeliveryFeeAreaRuleVOS();
        if (!CollectionUtils.isEmpty(tenantDeliveryFeeAreaRuleVOS)) {
            // 需要更新
            List<TenantDeliveryFeeArea> needAdd = new ArrayList<>();
            for (TenantDeliveryFeeAreaRuleVO tenantDeliveryFeeAreaRuleVO : tenantDeliveryFeeAreaRuleVOS) {
                List<TenantDeliveryFeeAreaVO> tenantDeliveryFeeAreaVOList = tenantDeliveryFeeAreaRuleVO.getTenantDeliveryFeeAreaVOList();
                String jsonRule = JSON.toJSONString(tenantDeliveryFeeAreaRuleVO.getDeliveryFeeAreaRuleDTOList());
                Integer groupId = Objects.nonNull(tenantDeliveryFeeAreaRuleVO.getGroupId()) ? tenantDeliveryFeeAreaRuleVO.getGroupId() : ++maxGroupId;

                List<TenantDeliveryFeeArea> needUpdateTenantDeliveryFeeAreas = tenantDeliveryFeeAreaVOList.stream().map(item -> {
                    TenantDeliveryFeeArea tenantDeliveryFeeArea = new TenantDeliveryFeeArea();
                    tenantDeliveryFeeArea.setTenantId(tenantId);
                    tenantDeliveryFeeArea.setRuleId(rule.getId());
                    tenantDeliveryFeeArea.setGroupId(groupId);
                    tenantDeliveryFeeArea.setProvince(item.getProvince());
                    tenantDeliveryFeeArea.setCity(item.getCity());
                    tenantDeliveryFeeArea.setArea(item.getArea());
                    tenantDeliveryFeeArea.setRule(jsonRule);
                    tenantDeliveryFeeArea.setDefaultPrice(tenantDeliveryFeeAreaRuleVO.getDefaultPrice());
                    return tenantDeliveryFeeArea;
                }).collect(Collectors.toList());
                needAdd.addAll(needUpdateTenantDeliveryFeeAreas);
            }

            // 批量新增
            tenantDeliveryFeeAreaDao.saveBatch(needAdd);
        }

        applicationEventPublisher.publishEvent(new TenantChangeEvent(Collections.singletonList(ruleDTO.getTenantId())));
        return CommonResult.ok(Boolean.TRUE);
    }

    private void checkDeliveryFeeRule(TenantDeliveryFeeRuleDTO ruleDTO) {
        TenantDeliveryFeeRuleEnums.Type type = TenantDeliveryFeeRuleEnums.Type.getByType(ruleDTO.getType());
        switch (type) {
            case CUSTOMIZE:
                AssertParam.notNull(ruleDTO.getDefaultPrice(), ResultDTOEnum.DEFAULT_DELIVER_FEE_NOT_NULL.getCode(), ResultDTOEnum.DEFAULT_DELIVER_FEE_NOT_NULL.getMessage());
                AssertParam.notNull(ruleDTO.getFreeNumber(), ResultDTOEnum.DEFAULT_FREE_NUMBER_NOT_NULL.getCode(), ResultDTOEnum.DEFAULT_FREE_NUMBER_NOT_NULL.getMessage());
                AssertParam.notNull(ruleDTO.getFreeType(), ResultDTOEnum.DEFAULT_FREE_TYPE_NOT_NULL.getCode(), ResultDTOEnum.DEFAULT_FREE_TYPE_NOT_NULL.getMessage());
                // 城市运费
                List<TenantDeliveryFeeAreaRuleVO> tenantDeliveryFeeAreaRuleVOS = ruleDTO.getTenantDeliveryFeeAreaRuleVOS();
                if (!CollectionUtils.isEmpty(tenantDeliveryFeeAreaRuleVOS)) {
                    tenantDeliveryFeeAreaRuleVOS.forEach(item -> {
                        List<DeliveryFeeAreaRuleDTO> deliveryFeeAreaRuleDTOList = item.getDeliveryFeeAreaRuleDTOList();
                        List<List<CategoryDTO>> categoryList = new ArrayList<>();
                        if (!CollectionUtils.isEmpty(deliveryFeeAreaRuleDTOList)) {
                            deliveryFeeAreaRuleDTOList.forEach(deliveryFeeAreaRuleDTO -> {
                                List<CategoryDTO> categoryDTOList = deliveryFeeAreaRuleDTO.getCategoryDTOList();
                                if (categoryList.contains(categoryDTOList)) {
                                    throw new DefaultServiceException(ResultDTOEnum.DELIVERY_FEE_CATETORY_NOT_REPAIR.getMessage());
                                } else {
                                    categoryList.add(categoryDTOList);
                                }
                            });
                        }
                    });
                }

                break;
            case ERROR:
                throw new DefaultServiceException(TenantDeliveryFeeRuleEnums.Type.ERROR.getDesc());
        }
    }

    @Override
    public TenantDeliveryFeeRuleDTO getDetailByTenantId(Long tenantId) {
        TenantDeliveryFeeRuleDTO dto = tenantDeliveryFeeRuleMapper.getDetailByTenantId(tenantId);
        TenantVO tenantVO = tenantService.queryTenantById(tenantId, TenantTypeEnum.BRAND.getCode());

        if (ObjectUtils.isEmpty(dto) && ObjectUtil.isNotNull(tenantVO)) {
            dto = new TenantDeliveryFeeRuleDTO();
            dto.setTenantId(tenantId);
            dto.setTenantName(tenantVO.getTenantName());
        }

        // 查询区域运费规则
        List<TenantDeliveryFeeArea> tenantDeliveryFeeAreas = tenantDeliveryFeeAreaDao.queryByTenantIdAndRuleId(tenantId, dto.getId());
        if (!CollectionUtils.isEmpty(tenantDeliveryFeeAreas)) {
            List<TenantDeliveryFeeAreaRuleDTO> tenantDeliveryFeeAreaRuleDTOS = tenantDeliveryFeeAreas.stream().map(
                    TenantDeliveryFeeAreaConvert::convertToDTO
            ).collect(Collectors.toList());

            Map<Integer, List<TenantDeliveryFeeAreaRuleDTO>> tenantDeliveryFeeAreaRuleDTOMap = tenantDeliveryFeeAreaRuleDTOS.stream().collect(Collectors.groupingBy(TenantDeliveryFeeAreaRuleDTO::getGroupId));
            List<TenantDeliveryFeeAreaRuleGroupDTO> tenantDeliveryFeeAreaRuleGroupDTOS = tenantDeliveryFeeAreaRuleDTOMap.values().stream().map(tenantDeliveryFeeAreaRuleDTOList -> {
                TenantDeliveryFeeAreaRuleGroupDTO tenantDeliveryFeeAreaRuleGroupDTO = new TenantDeliveryFeeAreaRuleGroupDTO();
                TenantDeliveryFeeAreaRuleDTO areaRuleDTO = tenantDeliveryFeeAreaRuleDTOList.get(NumberConstants.ZERO);
                tenantDeliveryFeeAreaRuleGroupDTO.setGroupId(areaRuleDTO.getGroupId());
                tenantDeliveryFeeAreaRuleGroupDTO.setDefaultPrice(areaRuleDTO.getDefaultPrice());
                tenantDeliveryFeeAreaRuleGroupDTO.setSize(tenantDeliveryFeeAreaRuleDTOList.size());
                tenantDeliveryFeeAreaRuleGroupDTO.setDeliveryFeeAreaRuleDTOList(areaRuleDTO.getDeliveryFeeAreaRuleDTOList());
                tenantDeliveryFeeAreaRuleGroupDTO.setTenantDeliveryFeeAreaRuleDTOS(tenantDeliveryFeeAreaRuleDTOList);
                return tenantDeliveryFeeAreaRuleGroupDTO;
            }).collect(Collectors.toList());

            dto.setTenantDeliveryFeeAreaRuleGroupDTOS(tenantDeliveryFeeAreaRuleGroupDTOS);
        }
        return dto;
    }
}
