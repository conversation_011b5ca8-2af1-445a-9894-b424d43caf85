package com.cosfo.oms.tenant.service;

import com.cosfo.oms.model.dto.LoginContextInfoDTO;
import com.cosfo.oms.tenant.model.dto.SendCodeDTO;
import com.cosfo.oms.tenant.model.dto.TenantAccountDTO;
import com.cosfo.oms.tenant.model.dto.TenantAccountListQueryDTO;
import com.cosfo.oms.tenant.model.dto.TenantAccountLoginDTO;
import com.cosfo.oms.tenant.model.po.TenantAccount;
import com.cosfo.oms.tenant.model.vo.TenantAccountLoginVO;
import com.cosfo.oms.tenant.model.vo.TenantAccountVO;
import com.github.pagehelper.PageInfo;

import java.util.Map;
import java.util.Set;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/2/27
 */
public interface TenantAccountService {


    /**
     * 登录
     *
     * @param tenantAccountLoginDTO
     * @return
     */
    TenantAccountLoginVO loginOld(TenantAccountLoginDTO tenantAccountLoginDTO);
    /**
     * 登录
     *
     * @param tenantAccountLoginDTO
     * @return
     */
    TenantAccountLoginVO loginV2(TenantAccountLoginDTO tenantAccountLoginDTO);

    /**
     * 更新用户信息
     *
     * @param tenantAccountDTO
     */
    Boolean updateUserInfo(TenantAccountDTO tenantAccountDTO, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 创建账号
     *
     * @param tenantAccountDTO
     * @return
     */
    Boolean create(TenantAccountDTO tenantAccountDTO);

    /**
     * 获取账号
     *
     * @return
     */
    TenantAccountVO getTenantAccountVO(Long authUserId);

    /**
     * 账号列表
     *
     * @param tenantAccountListQueryDTO
     * @param loginContextInfoDTO
     * @return
     */
    PageInfo<TenantAccountVO> page(TenantAccountListQueryDTO tenantAccountListQueryDTO, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 删除用户
     *
     * @param tenantAccountId
     */
    void remove(Long tenantAccountId, LoginContextInfoDTO loginContextInfoDTO);


    /**
     * 发送验证码
     * @param phone
     * @return
     */
    Boolean sendCode(String phone);

    /**
     * 校验验证码及修改密码
     * @param sendCodeDTO
     * @return
     */
    Boolean examineCode(SendCodeDTO sendCodeDTO);

    /**
     * 更改密码
     *
     * @param tenantAccountDTO
     * @return
     */
    Boolean updatePassword(TenantAccountDTO tenantAccountDTO);

    /**
     * 查询用户信息
     *
     * @param tenantId
     * @param userId
     * @return
     */
    TenantAccountVO queryAccountInfo(Long tenantId, Long userId);

    /**
     * 获取账户信息
     * @param userIds
     * @return
     */
    Map<Long, TenantAccount> queryAccountMap(Set<Long> userIds);

    /**
     * 生成账号密码
     *
     * @param phone
     * @return
     */
    String createPassword(String phone);


    /**
     * 登录超级账号
     *
     * @param targetTenantId
     * @param authUserId
     * @return
     */
    String loginSuperAccount(Long targetTenantId, Long authUserId);

}
