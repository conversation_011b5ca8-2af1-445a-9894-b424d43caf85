package com.cosfo.oms.tenant.model.vo;

import com.cosfo.oms.facade.dto.AuthUseLoginDTO;
import lombok.Data;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/2/27
 */
@Data
public class TenantAccountLoginVO extends AuthUseLoginDTO {
    /**
     * token
     */
    private String token;

    /**
     * 修改密码间隔天数
     */
    private Integer intervalDay;

    /**
     * 是否需要修改密码:true:是;false:否
     */
    private Boolean needChangePassword = Boolean.FALSE;
}
