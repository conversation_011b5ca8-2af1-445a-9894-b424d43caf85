package com.cosfo.oms.tenant.model.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.time.LocalTime;
import java.util.List;

@Data
public class TenantCloseTimeUpdateDTO {

    /**
     * 租户Id
     */
    @NotNull(message = "租户Id不能为空")
    private Long tenantId;


    /**
     * 更新省市区截单时间配置列表
     */
    @Valid
    @NotNull(message = "配置不能为空")
    private List<UpdateItemDTO> updateItemDTOList;

    @Data
    public static class UpdateItemDTO{

        /**
         * 最新截单时间
         */
        @JSONField(format="HH:mm")
        private LocalTime closeTime;


        /**
         * 省市区二维数组
         * [
         * 	[
         * 		"河南",
         * 		"洛阳市",
         * 		"栾川县"
         * 	],
         * 	[
         * 		"河南",
         * 		"洛阳市",
         * 		"新安县"
         * 	]
         * ]
         */
        private List<List<String>> areaList;
    }
}
