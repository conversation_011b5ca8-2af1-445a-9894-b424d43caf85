package com.cosfo.oms.tenant.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 */
@Data
public class TenantPrivilegesSimpleConfigVO implements Serializable {

    /**
     * 当前版本
     */
    private Integer saleVersion;

    /**
     * 到期时间
     */
    private LocalDate expireDate;

    /**
     * 增购功能集数量
     */
    private Integer functionCnt;

    /**
     * 增购功能集过期数量
     */
    private Integer expireFunctionCnt;

    private String customerManager;
}
