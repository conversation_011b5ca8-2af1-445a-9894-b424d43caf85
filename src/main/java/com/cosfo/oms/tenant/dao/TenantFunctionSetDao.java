package com.cosfo.oms.tenant.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.oms.tenant.model.dto.FunctionSetQueryDTO;
import com.cosfo.oms.tenant.model.po.TenantFunctionSet;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 功能集 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-22
 */
public interface TenantFunctionSetDao extends IService<TenantFunctionSet> {

    /**
     * 获取分页列表
     * @param page
     * @return
     */
    Page<TenantFunctionSet> selectByPage(FunctionSetQueryDTO queryDTO);


    List<TenantFunctionSet> list(FunctionSetQueryDTO queryDTO);


    /**
     * 获取详情
     * @param id
     * @return
     */
    TenantFunctionSet detail(Long id);

    /**
     * 新增
     * @param tenantFunctionSet
     * @return
     */
    Boolean add(TenantFunctionSet tenantFunctionSet);

    /**
     * 修改
     * @param tenantFunctionSet
     * @return
     */
    Boolean update(TenantFunctionSet tenantFunctionSet);

    /**
     * 删除
     * @param id
     * @return
     */
    Boolean delete(Long id);

    /**
     * 获取销售版本对应的功能集
     * @param id
     * @return
     */
    TenantFunctionSet getFunctionSetBySaleVersion(Integer saleVersion);

    /**
     * 名称是否已经存在
     */
    boolean isExistName(String name, Long id);

}
