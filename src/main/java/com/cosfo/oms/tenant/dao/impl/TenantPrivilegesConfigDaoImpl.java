package com.cosfo.oms.tenant.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.oms.common.context.PrivilegesConfigTypeEnum;
import com.cosfo.oms.tenant.model.dto.FuncSetCntDTO;
import com.cosfo.oms.tenant.model.po.TenantPrivilegesConfig;
import com.cosfo.oms.tenant.mapper.TenantPrivilegesConfigMapper;
import com.cosfo.oms.tenant.dao.TenantPrivilegesConfigDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 租户权益配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-26
 */
@Service
public class TenantPrivilegesConfigDaoImpl extends ServiceImpl<TenantPrivilegesConfigMapper, TenantPrivilegesConfig> implements TenantPrivilegesConfigDao {

    @Override
    public List<TenantPrivilegesConfig> getPrivilegesConfig(Long tenantId) {

        LambdaQueryWrapper<TenantPrivilegesConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TenantPrivilegesConfig::getTenantId, tenantId);
        queryWrapper.orderByAsc(TenantPrivilegesConfig::getTenantId, TenantPrivilegesConfig::getExpirationTime);
        return list(queryWrapper);
    }

    @Override
    public TenantPrivilegesConfig getPrivilegesConfigDetail(Long tenantId) {
        return null;
    }

    @Override
    public Boolean upsertPrivilegesConfig(TenantPrivilegesConfig configDTO) {
        return null;
    }

    @Override
    public Set<Long> getTenantByFunctionSet(Long functionSetId) {
        LambdaQueryWrapper<TenantPrivilegesConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TenantPrivilegesConfig::getFunctionSetId, functionSetId);
        return list(queryWrapper).stream().map(TenantPrivilegesConfig::getTenantId).collect(Collectors.toSet());
    }

    @Override
    public Boolean savePrivilegesConfigs(List<TenantPrivilegesConfig> tenantPrivilegesConfigs) {
        return saveBatch(tenantPrivilegesConfigs);
    }

    @Override
    public Map<Long, Integer> funcSetRefCnt(List<Long> funcSetIds) {
        List<FuncSetCntDTO> funcSetCntDTOS = baseMapper.funcSetRefCnt(funcSetIds);
        if (CollectionUtils.isEmpty(funcSetCntDTOS)) {
            return Collections.emptyMap();
        }
        return funcSetCntDTOS.stream().collect(Collectors.toMap(FuncSetCntDTO::getFunctionSetId, FuncSetCntDTO::getQuoteCnt));
    }

    @Override
    public List<Long> getTenantIdsByExpireDate() {
        LambdaQueryWrapper<TenantPrivilegesConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TenantPrivilegesConfig::getConfigType, PrivilegesConfigTypeEnum.SALE_VERSION);
        queryWrapper.orderByAsc(TenantPrivilegesConfig::getExpirationTime);
        return list(queryWrapper).stream().map(TenantPrivilegesConfig::getTenantId).collect(Collectors.toList());
    }

    @Override
    public List<TenantPrivilegesConfig> getTenantPrivilegesConfigByTenantIds(List<Long> tenantIds, PrivilegesConfigTypeEnum configTypeEnum) {
        LambdaQueryWrapper<TenantPrivilegesConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(TenantPrivilegesConfig::getTenantId, tenantIds);
        queryWrapper.eq(TenantPrivilegesConfig::getConfigType, configTypeEnum.getCode());
        return list(queryWrapper);
    }
}
