package com.cosfo.oms.tenant.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.oms.common.context.PrivilegesConfigTypeEnum;
import com.cosfo.oms.tenant.model.po.TenantPrivilegesConfig;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 租户权益配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-26
 */
public interface TenantPrivilegesConfigDao extends IService<TenantPrivilegesConfig> {

    /**
     * 获取当前租户权益配置
     */
    List<TenantPrivilegesConfig> getPrivilegesConfig(Long tenantId);

    /**
     * 获取当前租户权益配置详情
     */
    TenantPrivilegesConfig getPrivilegesConfigDetail(Long tenantId);

    /**
     * 更新当前租户权益配置
     */
    Boolean upsertPrivilegesConfig(TenantPrivilegesConfig configDTO);

    /**
     * 获取使用了该功能集的租户
     */
    Set<Long> getTenantByFunctionSet(Long functionSetId);

    /**
     * 批量保存
     */
    Boolean savePrivilegesConfigs(List<TenantPrivilegesConfig> tenantPrivilegesConfigs);

    /**
     * 功能使用数量
     * @param funcSetIds
     * @return
     */
    Map<Long, Integer> funcSetRefCnt(List<Long> funcSetIds);

    /**
     * 获取租户到期时间排序
     * @return
     */
    List<Long> getTenantIdsByExpireDate();

    /**
     * 获取租户权限配置
     * @param tenantIds
     * @param configTypeEnum
     * @return
     */
    List<TenantPrivilegesConfig> getTenantPrivilegesConfigByTenantIds(List<Long> tenantIds, PrivilegesConfigTypeEnum configTypeEnum);

}
