package com.cosfo.oms.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 结算信息返回数据
 * <AUTHOR>
 * @date : 2022/12/16 4:02
 */
@Data
public class SettleMentInfoDTO implements Serializable {
    /**
     * 微信配置信息
     */
    private List<QryWxConfDTO> qryWxConfDTO;
    /**
     * 支付宝配置信息
     */
    private List<QryAliConfDTO> qryAliConfDTO;
    /**
     * 结算配置信息
     */
    private List<SettleConfigDTO> settleConfig;
    /**
     * 银行账户配置信息
     */
    private List<CardInfoDTO> cardInfo;

    /**
     * 0代表特约商户，1代表鲜沐或者帆台
     */
    private Integer type;

    /**
     * 汇付基本信息
     */
    private HuiFuBasicDataReceiveDTO huiFuBasicDataReceiveDTO;
}
