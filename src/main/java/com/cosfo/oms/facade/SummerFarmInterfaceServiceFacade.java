package com.cosfo.oms.facade;

import com.cosfo.oms.common.exception.DefaultServiceException;
import com.google.common.collect.Lists;
import net.summerfarm.manage.client.saas.SaasInterfaceServiceProvider;
import net.summerfarm.manage.client.saas.req.SummerFarmSkuPriceInfoReq;
import net.summerfarm.manage.client.saas.req.SummerFarmSkuReq;
import net.summerfarm.manage.client.saas.req.SummerFarmSkuSupplyStatusReq;
import net.summerfarm.manage.client.saas.resp.SummerFarmSkuPriceInfoResp;
import net.summerfarm.manage.client.saas.resp.SummerFarmSkuResp;
import net.summerfarm.manage.client.saas.resp.SummerFarmSkuSupplyStatusResp;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/11/24
 */
@Component
public class SummerFarmInterfaceServiceFacade {
    @DubboReference
    private SaasInterfaceServiceProvider saasInterfaceServiceProvider;

    /**
     * 查询报价单区间
     * @param summerFarmSkuPriceInfoReqs 报价单查询
     * @return
     */
    @Deprecated
    public List<SummerFarmSkuPriceInfoResp> queryAdminSkuPricingInfo(List<SummerFarmSkuPriceInfoReq> summerFarmSkuPriceInfoReqs){
        DubboResponse<List<SummerFarmSkuPriceInfoResp>> response = saasInterfaceServiceProvider.queryAdminSkuPricingInfo(summerFarmSkuPriceInfoReqs);
        if (!response.isSuccess()) {
            throw new DefaultServiceException(response.getMsg());
        }

        return response.getData();
    }

    /**
     * 查询城市供应状态
     *
     * @param summerfarmSkuSupplyStatusInputs
     * @return
     */
    @Deprecated
    public List<SummerFarmSkuSupplyStatusResp> queryCitySupplyStatus(List<SummerFarmSkuSupplyStatusReq> summerfarmSkuSupplyStatusInputs){
        if(CollectionUtils.isEmpty(summerfarmSkuSupplyStatusInputs)){
            return Collections.emptyList();
        }
        List<SummerFarmSkuSupplyStatusResp> resultList = new ArrayList<>(summerfarmSkuSupplyStatusInputs.size());
        List<List<SummerFarmSkuSupplyStatusReq>> partition = Lists.partition(summerfarmSkuSupplyStatusInputs, 100);
        for (List<SummerFarmSkuSupplyStatusReq> summerFarmSkuSupplyStatusReqs : partition) {
            DubboResponse<List<SummerFarmSkuSupplyStatusResp>> response = saasInterfaceServiceProvider.queryCitySupplyStatus(summerFarmSkuSupplyStatusReqs);
            if (!response.isSuccess()) {
                throw new ProviderException(response.getMsg());
            }
            resultList.addAll(response.getData());
        }

        return resultList;
    }

    /**
     * 获取sku信息
     *
     * @param input
     * @return
     */
    public List<SummerFarmSkuResp> getSkuInfo(SummerFarmSkuReq input){
        DubboResponse<List<SummerFarmSkuResp>> response = saasInterfaceServiceProvider.getSkuInfo(input);
        if (!response.isSuccess()) {
            throw new DefaultServiceException(response.getMsg());
        }

        return response.getData();
    }
}
