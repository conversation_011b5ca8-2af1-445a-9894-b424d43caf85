package com.cosfo.oms.facade.order;

import com.cosfo.ordercenter.client.provider.OrderItemSnapshotQueryProvider;
import com.cosfo.ordercenter.client.resp.order.OrderItemSnapshotResp;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderItemSnapshotQueryFacade {

    @DubboReference
    private OrderItemSnapshotQueryProvider orderItemSnapshotQueryProvider;

    public OrderItemSnapshotResp queryByOrderItemId(Long orderItemId) {
        DubboResponse<OrderItemSnapshotResp> resp = orderItemSnapshotQueryProvider.queryByOrderItemId(orderItemId);
        if (!resp.isSuccess()) {
            log.error("查询订单明细快照失败, msg = {}", resp.getMsg());
            throw new ProviderException("查询订单快照失败");
        }
        return resp.getData();
    }

    public List<OrderItemSnapshotResp> queryByOrderItemIds(List<Long> orderItemIds) {
        DubboResponse<List<OrderItemSnapshotResp>> resp = orderItemSnapshotQueryProvider.queryByOrderItemIds(orderItemIds);
        if (!resp.isSuccess()) {
            log.error("查询订单明细快照失败, msg = {}", resp.getMsg());
            throw new ProviderException("查询订单快照失败");
        }
        return resp.getData();
    }
}
