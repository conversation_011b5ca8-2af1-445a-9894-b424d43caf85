package com.cosfo.oms.facade.convert;

import com.cosfo.erp.client.aftersale.req.OrderAfterSaleCheckTimeReq;
import com.cosfo.erp.client.aftersale.req.OrderAfterSaleQueryReq;
import com.cosfo.erp.client.aftersale.resp.OrderAfterSaleEnableApplyResp;
import com.cosfo.oms.facade.dto.OrderAfterSaleEnableApplyDTO;
import com.cosfo.oms.facade.input.OrderAfterSaleCheckTimeInput;
import com.cosfo.oms.facade.input.OrderAfterSaleInput;

import java.util.Collections;
import java.util.Objects;

/**
 * <AUTHOR>
 * @desc
 * @date 2023/2/8 21:02
 */
public class OrderAfterSaleConvert {
    public static OrderAfterSaleCheckTimeReq orderAfterSaleCheckTimeInput2Req(OrderAfterSaleCheckTimeInput input) {

        if (input == null) {
            return null;
        }
        OrderAfterSaleCheckTimeReq orderAfterSaleCheckTimeReq = new OrderAfterSaleCheckTimeReq();
        orderAfterSaleCheckTimeReq.setWarehouseType(input.getWarehouseType());
        orderAfterSaleCheckTimeReq.setTenantId(input.getTenantId());
        orderAfterSaleCheckTimeReq.setFinishedTime(input.getFinishedTime());
        return orderAfterSaleCheckTimeReq;
    }

    public static OrderAfterSaleQueryReq orderAfterSaleInput2Req(OrderAfterSaleInput orderAfterSaleInput) {
        if (orderAfterSaleInput == null) {
            return null;
        }
        OrderAfterSaleQueryReq orderAfterSaleQueryReq = new OrderAfterSaleQueryReq();
        orderAfterSaleQueryReq.setOrderIds(Objects.isNull(orderAfterSaleInput.getOrderId()) ? null : Collections.singletonList(orderAfterSaleInput.getOrderId()));
        orderAfterSaleQueryReq.setTenantId(orderAfterSaleInput.getTenantId());
        orderAfterSaleQueryReq.setOrderItemIds(Objects.isNull(orderAfterSaleInput.getOrderItemId()) ? null : Collections.singletonList(orderAfterSaleInput.getOrderItemId()));
        orderAfterSaleQueryReq.setStoreId(orderAfterSaleInput.getStoreId());
        orderAfterSaleQueryReq.setAfterSaleType(orderAfterSaleInput.getAfterSaleType());
        orderAfterSaleQueryReq.setServiceType(Objects.isNull(orderAfterSaleInput.getServiceType()) ? null : Collections.singletonList(orderAfterSaleInput.getServiceType()));
        orderAfterSaleQueryReq.setStatus(Objects.isNull(orderAfterSaleInput.getStatus()) ? null : Collections.singletonList(orderAfterSaleInput.getStatus()));
        orderAfterSaleQueryReq.setResponsibilityType(orderAfterSaleInput.getResponsibilityType());
        orderAfterSaleQueryReq.setProcessFlag(orderAfterSaleInput.getProcessFlag());
        orderAfterSaleQueryReq.setAccountId(orderAfterSaleInput.getAccountId());
        return orderAfterSaleQueryReq;
    }

    public static OrderAfterSaleEnableApplyDTO orderAfterSaleEnableResp2Dto(OrderAfterSaleEnableApplyResp data) {

        if (data == null) {
            return null;
        }
        OrderAfterSaleEnableApplyDTO orderAfterSaleEnableApplyDTO = new OrderAfterSaleEnableApplyDTO();
        orderAfterSaleEnableApplyDTO.setEnableApplyAmount(data.getEnableApplyAmount());
        orderAfterSaleEnableApplyDTO.setEnableApplyQuantity(data.getEnableApplyQuantity());
        orderAfterSaleEnableApplyDTO.setEnableApplyPrice(data.getEnableApplyPrice());
        return orderAfterSaleEnableApplyDTO;
    }
}
