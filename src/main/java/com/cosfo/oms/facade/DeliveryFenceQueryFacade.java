package com.cosfo.oms.facade;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.wnc.client.provider.fence.DeliveryFenceQueryProvider;
import net.summerfarm.wnc.client.req.AreaQueryReq;
import net.summerfarm.wnc.client.req.DeliveryFenceQueryReq;
import net.summerfarm.wnc.client.req.StoreQueryReq;
import net.summerfarm.wnc.client.resp.AreaQueryResp;
import net.summerfarm.wnc.client.resp.DeliveryFenceResp;
import net.summerfarm.wnc.client.resp.StoreQueryResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * @Author: fansongsong
 * @Date: 2023-09-22
 * @Description:
 */
@Component
@Slf4j
public class DeliveryFenceQueryFacade {

    @DubboReference
    private DeliveryFenceQueryProvider deliveryFenceQueryProvider;

    /**
     * 查询配送围栏信息
     *
     * @param deliveryFenceQueryReq 配送围栏查询参数
     * @return 配送围栏信息
     */
    public DeliveryFenceResp queryDeliveryFence(DeliveryFenceQueryReq deliveryFenceQueryReq) {
        DubboResponse<DeliveryFenceResp> response = deliveryFenceQueryProvider.queryDeliveryFence(deliveryFenceQueryReq);
        if (!response.isSuccess()) {
            throw new ProviderException(response.getMsg());
        }
        return response.getData();
    }

    public AreaQueryResp getArea(String city, String area){
        AreaQueryReq req = new AreaQueryReq();
        req.setArea(area);
        req.setCity(city);
        DubboResponse<AreaQueryResp> areaQueryResp = deliveryFenceQueryProvider.queryAreaByAddress(req);
        if (areaQueryResp.isSuccess()) {
            return areaQueryResp.getData();
        }
        throw new BizException(areaQueryResp.getMsg());
    }

    public StoreQueryResp queryStoreByAddress(Long tenantId, Long storeId, String city, String area){
        StoreQueryReq req = new StoreQueryReq();
        req.setTenantId(tenantId);
        req.setContactId(storeId);
        req.setCity(city);
        req.setArea(area);
        DubboResponse<StoreQueryResp> areaQueryResp = deliveryFenceQueryProvider.queryStoreByAddress(req);
        if (areaQueryResp.isSuccess()) {
            return areaQueryResp.getData();
        }
        throw new BizException(areaQueryResp.getMsg());
    }


    public StoreQueryResp queryStoreByAddress(String city, String area){
        StoreQueryReq req = new StoreQueryReq();
        req.setCity(city);
        req.setArea(area);
        DubboResponse<StoreQueryResp> areaQueryResp = deliveryFenceQueryProvider.queryStoreByAddress(req);
        if (areaQueryResp.isSuccess()) {
            return areaQueryResp.getData();
        }
        throw new BizException(areaQueryResp.getMsg());
    }
}
