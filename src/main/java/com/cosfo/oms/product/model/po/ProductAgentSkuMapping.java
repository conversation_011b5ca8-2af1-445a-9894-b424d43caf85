package com.cosfo.oms.product.model.po;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * product_agent_sku_mapping
 *
 * <AUTHOR>
@Data
public class ProductAgentSkuMapping implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * 代理供应商租户Id
     */
    private Long agentTenantId;

    /**
     * 代理skuId
     */
    private Long agentSkuId;

    /**
     * create time
     */
    private Date createTime;

    /**
     * update time
     */
    private Date updateTime;

    /**
     * 鲜沐sku编码
     */
    private String agentSkuCode;

    private static final long serialVersionUID = 1L;
}