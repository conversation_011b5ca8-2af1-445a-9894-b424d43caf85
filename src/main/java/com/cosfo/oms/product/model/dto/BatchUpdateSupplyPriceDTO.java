package com.cosfo.oms.product.model.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/9/15
 */
@Data
public class BatchUpdateSupplyPriceDTO {
    /**
     * 批量更改价格类型 0 批量改价 1批量加价 2批量件价
     */
    private Integer type;
    /**
     * 价格
     */
    private BigDecimal price;
    /**
     * 城市报价单Id
     */
    private List<Long> citySupplyPriceIds;
}
