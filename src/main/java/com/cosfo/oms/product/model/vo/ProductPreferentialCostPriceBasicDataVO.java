package com.cosfo.oms.product.model.vo;

import com.cosfo.oms.common.constant.NumberConstants;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ProductPreferentialCostPriceBasicDataVO {
    /**
     * 可用数量
     */
    private Integer availableQuantity;
    /**
     * 已消耗 数量
     */
    private Integer usedQuantity;

    public static ProductPreferentialCostPriceBasicDataVO getEmptyDataVO() {
        ProductPreferentialCostPriceBasicDataVO productPreferentialCostPriceBasicDataVO = new ProductPreferentialCostPriceBasicDataVO();
        productPreferentialCostPriceBasicDataVO.setAvailableQuantity(NumberConstants.ZERO);
        productPreferentialCostPriceBasicDataVO.setUsedQuantity(NumberConstants.ZERO);
        return productPreferentialCostPriceBasicDataVO;
    }
}
