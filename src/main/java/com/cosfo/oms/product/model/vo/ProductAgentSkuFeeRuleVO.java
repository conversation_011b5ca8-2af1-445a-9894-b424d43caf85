package com.cosfo.oms.product.model.vo;

import com.cosfo.oms.product.model.dto.ProductAgentSkuFeeRuleDTO;
import lombok.Data;
import org.omg.CORBA.PRIVATE_MEMBER;

import java.beans.IntrospectionException;
import java.math.BigDecimal;
import java.util.List;

@Data
public class ProductAgentSkuFeeRuleVO {
    /**
     * 收费规则0按比例1按件数
     */
    private Integer type;
    /**
     * 规则明细
     */
    private List<ProductAgentSkuFeeRuleDetailVO> details;

    /**
     * @see com.cosfo.oms.common.context.ProductAutomaticIncreasePriceFlagEnum
     */
    private Integer automaticIncreasePriceFlag;

}
