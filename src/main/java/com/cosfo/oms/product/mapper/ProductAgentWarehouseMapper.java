package com.cosfo.oms.product.mapper;

import com.cosfo.oms.product.model.po.ProductAgentWarehouse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/11/4
 */
@Mapper
public interface ProductAgentWarehouseMapper {
    /**
     * 删除
     *
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 新增
     *
     * @param record
     * @return
     */
    int insert(ProductAgentWarehouse record);

    /**
     * 新增
     *
     * @param record
     * @return
     */
    int insertSelective(ProductAgentWarehouse record);

    /**
     * 查询
     *
     * @param id
     * @return
     */
    ProductAgentWarehouse selectByPrimaryKey(Long id);

    /**
     * 更新
     *
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(ProductAgentWarehouse record);

    /**
     * 更新
     *
     * @param record
     * @return
     */
    int updateByPrimaryKey(ProductAgentWarehouse record);

    /**
     * 查询品牌方代仓商品使用仓
     *
     * @param tenantId
     * @return
     */
    List<ProductAgentWarehouse> selectByTenantId(@Param("tenantId") Long tenantId);

    /**
     * 根据租户删除绑定仓库
     *
     * @param tenantId
     */
    void deleteByTenantId(@Param("tenantId") Long tenantId);

    /**
     * 根据仓库Id删除
     *
     * @param warehouseIds
     */
    void deleteByWarehouseIds(@Param("warehouseIds") List<Long> warehouseIds, @Param("tenantId") Long tenantId);
}