package com.cosfo.oms.product.service;

import com.cosfo.oms.product.model.dto.ProductPreferentialCostPriceDTO;
import com.cosfo.oms.product.model.dto.ProductPreferentialCostPriceDeleteDTO;
import com.cosfo.oms.product.model.vo.ProductPreferentialCostPriceBasicDataVO;
import com.cosfo.oms.product.model.vo.ProductPreferentialCostPriceVO;
import com.cosfo.oms.tenant.model.dto.PreferentialCostPriceQueryDTO;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2024-02-21
 * @Description:
 */
public interface ProductPreferentialCostPriceService {

    /**
     * 分页查询省心订列表
     *
     * @param queryDTO
     * @return
     */
    PageInfo<ProductPreferentialCostPriceVO> listPreferentialCostPrice(PreferentialCostPriceQueryDTO queryDTO);

    /**
     * 查询基础数据汇总
     *
     * @param queryDTO
     * @return
     */
    ProductPreferentialCostPriceBasicDataVO queryBasicData(PreferentialCostPriceQueryDTO queryDTO);

    /**
     * 删除租户报价货品的省心订配置
     *
     * @param dto
     * @return
     */
    Boolean deleteSkuPreferentialCostPrice(ProductPreferentialCostPriceDeleteDTO dto);

    /**
     * 写入品牌方省心定价格
     * @param dtoList
     * @return
     */
    void upsertSkuCostPrice(List<ProductPreferentialCostPriceDTO> dtoList);

    /**
     * 租户下是否存在有效状态的货品省心订配置
     * @param tenantId
     * @return
     */
    Boolean existValidConfigQuery(Long tenantId);
}
