package com.cosfo.oms.wechat.bean.wxa;

import com.cosfo.oms.wechat.bean.BaseResult;
import lombok.Data;

import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-04-26
 * @Description:
 */
@Data
public class PluginListResult extends BaseResult {

    private List<PluginResult> plugin_list;

    @Data
    public static class PluginResult {
        /**
         * 插件 appId
         */
        private String appid;

        /**
         * 插件状态。1表示申请中；2表示申请通过；3表示被拒绝；4表示申请已超时
         */
        private Integer status;

        /**
         * 插件昵称
         */
        private String nickname;

        /**
         * 插件头像
         */
        private String headimgurl;
    }
}
