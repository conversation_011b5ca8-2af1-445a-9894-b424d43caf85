package com.cosfo.oms.wechat.mapper;

import com.cosfo.oms.wechat.model.po.WechatAuthorizer;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Mapper
public interface WechatAuthorizerMapper {
    int deleteByPrimaryKey(Long id);

    int insert(WechatAuthorizer record);

    int insertSelective(WechatAuthorizer record);

    WechatAuthorizer selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(WechatAuthorizer record);

    int updateByPrimaryKey(WechatAuthorizer record);

    //根据appId更新授权信息
    int updateByAppId(WechatAuthorizer record);

    //根据授权码获取授权方信息
    WechatAuthorizer selectByAuthCode(@Param("authCode") String authCode);

    //定时任务批量获取授权方
    List<WechatAuthorizer> selectByRefreshToken(@Param("appId") String appId, @Param("status") Integer status, @Param("platformAppId") String platformAppId);

    //根据appId获取授权方
    WechatAuthorizer selectByAppId(@Param("appId") String appId, @Param("platformAppId") String platformAppId);

    //根据appId获取授权方
    WechatAuthorizer selectOneByAppId(@Param("appId") String appId, @Param("platformAppId") String platformAppId);

    //查询最后2分钟授权过来没有关联的商户进行关联
    List<WechatAuthorizer> selectByTime(@Param("status") Integer status, @Param("createTime") Date createTime);

    //获取授权小程序appId列表
    List<WechatAuthorizer> getAuthTenants(@Param("platformAppId") String platformAppId);

    //根据appId获取授权方列表
    List<WechatAuthorizer> selectListAppIds(@Param("appIds") List<String> appIds);

    // 根据appId获取授权方列表
    List<WechatAuthorizer> selectListAppIdsAndPlat(@Param("appIds") List<String> appIds, @Param("platformAppId") String platformAppId);


}