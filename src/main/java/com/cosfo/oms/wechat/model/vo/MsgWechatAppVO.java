package com.cosfo.oms.wechat.model.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 微信消息模版相关小程序查询VO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-31
 */
@Data
public class MsgWechatAppVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key（模板id）
     */
    private Long id;

    /**
     * 商城id
     */
    private Long mallId;

    /**
     * 商城名称
     */
    private String mallName;

    /**
     * 查询类型 0未关联、1已关联
     */
    private Integer type;

    /**
     * 0创建失败 1成功
     */
    private Integer successFlag;

    /**
     * 当前页码
     */
    private Integer pageIndex;

    /**
     * 页面大小
     */
    private Integer pageSize;

    /**
     * 是否有推送权限
     */
    private Integer availableStatus;

    /**
     * 平台id
     */
    private String platformAppId;
}
