package com.cosfo.oms.wechat.service;

import com.cosfo.oms.common.result.ResultDTO;
import com.cosfo.oms.wechat.bean.wxa.WxTemplateDTO;
import com.cosfo.oms.wechat.model.dto.DraftDto;
import com.cosfo.oms.wechat.model.dto.PrivateSettingDto;
import com.cosfo.oms.wechat.model.dto.TenantTemplateDto;
import com.cosfo.oms.wechat.model.po.WechatTemplatePackage;
import com.cosfo.oms.wechat.model.vo.BindTesterVo;
import com.cosfo.oms.wechat.model.vo.CommitAuditVo;
import com.cosfo.oms.wechat.model.vo.CommitCodePreVo;
import com.cosfo.oms.wechat.model.vo.PrivateSettingVo;

import java.io.IOException;
import java.util.List;

public interface WeixinTemplateService {

    /**
     * 发布代码 获取授权开发小程序的租户列表
     */
    List<TenantTemplateDto> getAuthTenants();

    /**
     * 发布代码 获取代码模板列表
     */
    List<WxTemplateDTO> getTemplateList();

    /**
     * 发布代码 获取微信小程序草稿列表
     */
    List<DraftDto> getDraftList();

    /**
     * 发布代码 获取体验版二维码
     */
    byte[] getTpQrCode(String appId, String path) throws IOException;
//
//    /**
//     * 绑定微信用户为体验者
//     */
//    void bindTester(BindTesterVo bindTesterVo);

    /**
     * 发布代码 把草稿选为模板
     */
    ResultDTO addTpDraftToTemplate(String draftId);

    /**
     * 发布代码 删除模板
     */
    ResultDTO delTpTemplate(String templateId);

    /**
     * 上传小程序代码并生成体验版 发布代码
     */
    ResultDTO commitCodeExperience(CommitCodePreVo commitCodePreVo) throws Exception;

//    /**
//     * 查询小程序用户隐私保护指引
//     */
//    PrivateSettingDto getPrivacySetting(String appId, Integer privacyVer) throws Exception;

    /**
     * 发布代码 设置小程序用户隐私保护指引
     */
    ResultDTO setPrivacySetting(PrivateSettingVo privateSettingVo, int ver) throws Exception;

    /**
     * 提交审核接口，此接口传appId为根据appId进行提交，不传为全量状态为开发版本的提交审核 发布代码
     */
    ResultDTO submitAuditPackage(CommitAuditVo commitAuditVo) throws Exception;

    /**
     * 批量为所有小程序申请设置订单页path信息
     */
    ResultDTO<?> applySetOrderPath(List<String> appIds);

    /**
     * 发布已过审的小程序 发布代码
     */
    ResultDTO releasePackage(CommitAuditVo commitAuditVo) throws Exception;

    /**
     * 版本回退 发布代码
     */
    ResultDTO rollbackPackage(CommitAuditVo commitAuditVo) throws Exception;

    /**
     * 审核撤回
     */
    ResultDTO withdrawPackage(CommitAuditVo commitAuditVo) throws Exception;


    /**
     * 设置小程序服务器域名 发布代码
     */
    ResultDTO setModifyDomain(String appId) throws Exception;
//
//    /**
//     * 设置小程序业务域名
//     */
//    void setModifyWebviewDomain(String appId) throws Exception;

    /**
     * 设置小程序域名
     */
    ResultDTO initDomain(String appId) throws Exception;


    /**
     * 调用https://developers.weixin.qq.com/doc/oplatform/openApi/OpenApiDoc/miniprogram-management/code-management/getAuditStatus.html来获取最新的审核状态；
     *
     * @param appId 可以为空，如果为空，则会获取全部处于审核中的app；
     * @return
     */
    List<WechatTemplatePackage> syncAuditStatus(String appId);
}
