package com.cosfo.oms.wechat.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.oms.common.constant.DouGongPluginConstant;
import com.cosfo.oms.common.constant.NumberConstants;
import com.cosfo.oms.common.context.*;
import com.cosfo.oms.common.result.ResultDTO;
import com.cosfo.oms.common.result.ResultDTOEnum;
import com.cosfo.oms.common.utils.AssertBiz;
import com.cosfo.oms.common.utils.AssertParam;
import com.cosfo.oms.system.service.Impl.DictSysParameter;
import com.cosfo.oms.system.service.SystemParametersService;
import com.cosfo.oms.tenant.mapper.TenantAuthConnectionMapper;
import com.cosfo.oms.tenant.service.TenantInfoService;
import com.cosfo.oms.wechat.api.ComponentApi;
import com.cosfo.oms.wechat.api.WxaApi;
import com.cosfo.oms.wechat.bean.*;
import com.cosfo.oms.wechat.bean.wxa.*;
import com.cosfo.oms.wechat.mapper.WechatAuthorizerMapper;
import com.cosfo.oms.wechat.mapper.WechatLiteConfigMapper;
import com.cosfo.oms.wechat.mapper.WechatTemplatePackageMapper;
import com.cosfo.oms.wechat.model.dto.*;
import com.cosfo.oms.wechat.model.po.WechatAuthorizer;
import com.cosfo.oms.wechat.model.po.WechatLiteConfig;
import com.cosfo.oms.wechat.model.po.WechatTemplatePackage;
import com.cosfo.oms.wechat.model.resp.WechatQueryPluginsResponse;
import com.cosfo.oms.wechat.model.vo.CommitAuditVo;
import com.cosfo.oms.wechat.model.vo.CommitCodePreVo;
import com.cosfo.oms.wechat.model.vo.PrivateSettingVo;
import com.cosfo.oms.wechat.req.WechatQueryPluginsRequest;
import com.cosfo.oms.wechat.service.AuthorizerService;
import com.cosfo.oms.wechat.service.WeixinTemplateService;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.Nullable;
import javax.annotation.Resource;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.usercenter.client.tenant.resp.TenantResultResp;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;


@Service
@Slf4j
public class WeixinTemplateServiceImpl implements WeixinTemplateService {

    private static final String ORDER_PATH = "pages/order/index";
    public static final String ORDER_PATH_VIDEO_URL = "https://qiniu.cosfo.cn/%E9%A3%9E%E4%B9%A620231026-134203.mp4";

    @Resource
    WechatAuthorizerMapper wechatAuthorizerMapper;
    @Resource
    TenantAuthConnectionMapper tenantAuthConnectionMapper;
    @Resource
    AuthorizerService authorizerService;
    @Resource
    WechatTemplatePackageMapper templatePackageMapper;
    @Resource
    WechatLiteConfigMapper wechatLiteConfigMapper;
    @Resource
    DictSysParameter dictSysParameter;
    @Resource
    SystemParametersService systemParametersService;
    @Resource
    private TenantInfoService tenantInfoService;


    @Override
    public List<TenantTemplateDto> getAuthTenants() {
        List<TenantTemplateDto> templateInfos = tenantAuthConnectionMapper.selectTenantInfos(dictSysParameter.getParameter().getTpAppId());
        if (CollectionUtils.isEmpty(templateInfos)) {
            return Lists.newArrayList();
        }
        //补充tenant信息
        Map<Long, TenantResultResp> tenantInfoMap = tenantInfoService.getTenantInfoMap(templateInfos.stream().map(TenantTemplateDto::getTenantId).collect(Collectors.toList()));


        List<Integer> onlineStatus = new ArrayList<>();
        onlineStatus.add(VersionStatus.ONLINE_VERSION.getCode());
        List<Integer> auditStatus = new ArrayList<>();
        auditStatus.add(VersionStatus.AUDIT_ING.getCode());
        auditStatus.add(VersionStatus.AUDIT_UNPASS.getCode());
        auditStatus.add(VersionStatus.AUDIT_PASS.getCode());
        auditStatus.add(VersionStatus.AUDIT_DELAY.getCode());
        List<Integer> expStatus = new ArrayList<>();
        expStatus.add(VersionStatus.DEV_VERSION.getCode());

        templateInfos.forEach(authorizer -> {
            TenantResultResp tenantInfoDTO = tenantInfoMap.get(authorizer.getTenantId());
            if (tenantInfoDTO != null) {
                authorizer.setTenantName(tenantInfoDTO.getTenantName());
            }
            List<WechatTemplatePackage> OnlinePackages = templatePackageMapper.selectPackageListByStatus(
                    authorizer.getAppId(), onlineStatus);
            if (!CollectionUtils.isEmpty(OnlinePackages)) {
                authorizer.setOnlineVersionDto(
                        WxConverter.toOnlineDto(OnlinePackages.get(NumberConstants.ZERO)));
            }
            List<WechatTemplatePackage> auditPackages = templatePackageMapper.selectPackageListByStatus(
                    authorizer.getAppId(), auditStatus);
            if (!CollectionUtils.isEmpty(auditPackages)) {
                authorizer.setAuditVersionDto(
                        WxConverter.toAuditDto(auditPackages.get(NumberConstants.ZERO)));
            }
            List<WechatTemplatePackage> expPackages = templatePackageMapper.selectPackageListByStatus(
                    authorizer.getAppId(), expStatus);
            if (!CollectionUtils.isEmpty(expPackages)) {
                authorizer.setExpVersionDto(
                        WxConverter.toExpDto(expPackages.get(NumberConstants.ZERO)));
            }
        });

        return templateInfos;
    }

    @Override
    public List<WxTemplateDTO> getTemplateList() {
        WechatAuthorizerDto wechatAuthorizerDto = authorizerService.getAuthorizer();
        AssertBiz.notNull(wechatAuthorizerDto, ResultDTOEnum.PARAMETER_MISSING.getCode(),
                "app对象为空");

        List<TemplateItem> templateList = WxaApi.gettemplatelist(
                wechatAuthorizerDto.getAccessToken()).getTemplate_list();
        if (!CollectionUtils.isEmpty(templateList)) {
            return templateList.stream()
                    .sorted(Comparator.comparing(TemplateItem::getTemplate_id, Comparator.reverseOrder()))
                    .map(WxConverter::toDto).collect(Collectors.toList());
        } else {
            return null;
        }
    }

    @Override
    public List<DraftDto> getDraftList() {
        WechatAuthorizerDto wechatAuthorizerDto = authorizerService.getAuthorizer();
        List<TemplateItem> response = WxaApi.gettemplatedraftlist(
                wechatAuthorizerDto.getAccessToken()).getDraft_list();
        if (!CollectionUtils.isEmpty(response)) {
            return response.stream()
                    .sorted(Comparator.comparing(TemplateItem::getDraft_id).reversed())
                    .map(WxConverter::toDraftDto).collect(Collectors.toList());
        } else {
            return null;
        }
    }

    @Override
    public byte[] getTpQrCode(String appId, String path) throws IOException {
        AssertParam.notNull(appId, ResultDTOEnum.PARAMETER_MISSING.getCode(), "appId is null");
        WechatAuthorizer wechatAuthorizer = authorizerService.getAccessTokenByAppId(appId);
        AssertBiz.notNull(wechatAuthorizer, ResultDTOEnum.PARAMETER_MISSING.getCode(),
                "app对象为空");
        return WxaApi.get_qrcode(wechatAuthorizer.getAccessToken(), path);
    }
//
//    @Override
//    public void bindTester(BindTesterVo bindTesterVo) {
//        Assert.notNull(bindTesterVo.getAppId(), "appId is null");
//        WechatAuthorizer wechatAuthorizer = authorizerService.getAccessTokenByAppId(bindTesterVo.getAppId());
//        WxaApi.bind_tester(wechatAuthorizer.getAccessToken(), bindTesterVo.getWeChatId());
//    }

    @Override
    public ResultDTO addTpDraftToTemplate(String draftId) {
        log.info("开始添加草稿到模板，draftId: {}", draftId);

        try {
            WechatAuthorizerDto wechatAuthorizerDto = authorizerService.getAuthorizer();
            AssertBiz.notNull(wechatAuthorizerDto, ResultDTOEnum.PARAMETER_MISSING.getCode(), "授权信息为空");

            log.info("调用微信API添加草稿到模板，accessToken: {}, draftId: {}",
                    wechatAuthorizerDto.getAccessToken(), draftId);

            BaseResult result = WxaApi.addtotemplate(wechatAuthorizerDto.getAccessToken(), draftId);

            log.info("微信API响应结果: {}", JSON.toJSONString(result));

            if (result == null || !result.successed()) {
                String errorMsg = String.format("添加草稿到模板失败，draftId: %s, errcode: %s, errmsg: %s",
                        draftId, result != null ? result.getErrcode() : "null",
                        result != null ? result.getErrmsg() : "响应为空");
                log.error(errorMsg);
                return ResultDTO.fail(errorMsg);
            }

            log.info("添加草稿到模板成功，draftId: {}", draftId);
            return ResultDTO.success();

        } catch (Exception e) {
            String errorMsg = String.format("添加草稿到模板异常，draftId: %s, 异常信息: %s", draftId, e.getMessage());
            log.error(errorMsg, e);
            return ResultDTO.fail(errorMsg);
        }
    }

    @Override
    public ResultDTO delTpTemplate(String templateId) {
        log.info("开始删除模板，templateId: {}", templateId);

        try {
            WechatAuthorizerDto wechatAuthorizerDto = authorizerService.getAuthorizer();
            AssertBiz.notNull(wechatAuthorizerDto, ResultDTOEnum.PARAMETER_MISSING.getCode(), "授权信息为空");

            log.info("调用微信API删除模板，accessToken: {}, templateId: {}",
                    wechatAuthorizerDto.getAccessToken(), templateId);

            BaseResult result = WxaApi.deletetemplate(wechatAuthorizerDto.getAccessToken(), templateId);

            log.info("微信API响应结果: {}", JSON.toJSONString(result));

            if (result == null || !result.successed()) {
                String errorMsg = String.format("删除模板失败，templateId: %s, errcode: %s, errmsg: %s",
                        templateId, result != null ? result.getErrcode() : "null",
                        result != null ? result.getErrmsg() : "响应为空");
                log.error(errorMsg);
                return ResultDTO.fail(errorMsg);
            }

            log.info("删除模板成功，templateId: {}", templateId);
            return ResultDTO.success();

        } catch (Exception e) {
            String errorMsg = String.format("删除模板异常，templateId: %s, 异常信息: %s", templateId, e.getMessage());
            log.error(errorMsg, e);
            return ResultDTO.fail(errorMsg);
        }
    }

    @Override
    public ResultDTO commitCodeExperience(CommitCodePreVo commitCodePreVo) throws Exception {
        log.info("开始生成体验版，请求参数: {}", JSON.toJSONString(commitCodePreVo));

        try {
            AssertParam.notNull(commitCodePreVo.getTemplateId(),
                    ResultDTOEnum.PARAMETER_MISSING.getCode(), "templateId is null");
            AssertParam.notNull(commitCodePreVo.getUserVersion(),
                    ResultDTOEnum.PARAMETER_MISSING.getCode(), "userVersion is null");
            AssertParam.notNull(commitCodePreVo.getUserDesc(),
                    ResultDTOEnum.PARAMETER_MISSING.getCode(), "userDesc is null");

            List<WechatAuthorizer> authorizerList = new ArrayList<>();
            if (!StringUtils.isEmpty(commitCodePreVo.getAppId())) {
                WechatAuthorizer wechatAuthorizer = authorizerService.getAccessTokenByAppId(
                        commitCodePreVo.getAppId());
                if (ObjectUtils.isEmpty(wechatAuthorizer)) {
                    String errorMsg = String.format("生成体验版失败，appId不存在对象，appId: %s", commitCodePreVo.getAppId());
                    log.error(errorMsg);
                    return ResultDTO.fail(errorMsg);
                }
                authorizerList.add(wechatAuthorizer);
            } else {
                authorizerList = wechatAuthorizerMapper.getAuthTenants(
                        dictSysParameter.getParameter().getTpAppId());
            }

            if (CollectionUtils.isEmpty(authorizerList)) {
                String errorMsg = "生成体验版失败，未找到可操作的小程序";
                log.error(errorMsg);
                return ResultDTO.fail(errorMsg);
            }

            List<String> failedAppIds = new ArrayList<>();
            List<String> successAppIds = new ArrayList<>();

            for (WechatAuthorizer info : authorizerList) {
                try {
                    log.info("生成体验版的appId appId={}", info.getAppId());
                    Commit commit = new Commit();
                    String extJson = assemblyExtJson(info.getAppId(), info.getAccessToken());
                    commit.setExt_json(extJson);
                    commit.setTemplate_id(commitCodePreVo.getTemplateId().toString());
                    commit.setUser_desc(commitCodePreVo.getUserDesc());
                    commit.setUser_version(commitCodePreVo.getUserVersion());

                    log.info("调用微信API生成体验版，appId: {}, accessToken: {}, 请求参数：{}",
                            info.getAppId(), info.getAccessToken(), JSON.toJSONString(commit));

                    BaseResult result = WxaApi.commit(info.getAccessToken(), commit);

                    log.info("微信API响应结果，appId: {}, 响应: {}", info.getAppId(), JSON.toJSONString(result));

                    if (result == null || !result.successed()) {
                        String errorMsg = String.format("appId: %s, errcode: %s, errmsg: %s",
                                info.getAppId(), result != null ? result.getErrcode() : "null",
                                result != null ? result.getErrmsg() : "响应为空");
                        log.error("生成体验版失败，{}", errorMsg);
                        failedAppIds.add(errorMsg);
                        continue;
                    }

                    // 更新数据库记录
                    log.info("appId:" + info.getAppId() + "----" + "status:" + VersionStatus.DEV_VERSION.getCode());
                    WechatTemplatePackage wechatTemplatePackage = templatePackageMapper.selectPackageByStatus(
                            info.getAppId(), VersionStatus.DEV_VERSION.getCode());
                    if (ObjectUtils.isEmpty(wechatTemplatePackage)) {
                        WechatTemplatePackage Package = new WechatTemplatePackage();
                        Package.setAppid(info.getAppId());
                        Package.setPkgStatus(VersionStatus.DEV_VERSION.getCode());
                        Package.setCreateTime(LocalDateTime.now());
                        Package.setVersion(commitCodePreVo.getUserVersion());
                        Package.setTemplateId(commitCodePreVo.getTemplateId());
                        Package.setPkgDesc(commitCodePreVo.getUserDesc());
                        templatePackageMapper.insertSelective(Package);
                    } else {
                        wechatTemplatePackage.setPkgDesc(commitCodePreVo.getUserDesc());
                        wechatTemplatePackage.setTemplateId(commitCodePreVo.getTemplateId());
                        wechatTemplatePackage.setVersion(commitCodePreVo.getUserVersion());
                        wechatTemplatePackage.setUpdateTime(LocalDateTime.now());
                        templatePackageMapper.updateByPrimaryKeySelective(wechatTemplatePackage);
                    }

                    successAppIds.add(info.getAppId());
                    log.info("生成体验版成功，appId: {}", info.getAppId());

                } catch (Exception e) {
                    String errorMsg = String.format("appId: %s, 异常信息: %s", info.getAppId(), e.getMessage());
                    log.error("生成体验版异常，{}", errorMsg, e);
                    failedAppIds.add(errorMsg);
                }
            }

            log.info("***********************生成体验版结束***********************");
            log.info("生成体验版操作完成，成功: {} 个，失败: {} 个", successAppIds.size(), failedAppIds.size());

            if (!failedAppIds.isEmpty()) {
                String errorMsg = String.format("生成体验版部分失败，成功: %d 个，失败: %d 个，失败详情: %s",
                        successAppIds.size(), failedAppIds.size(), String.join("; ", failedAppIds));
                log.error(errorMsg);
                return ResultDTO.fail(errorMsg);
            }

            return ResultDTO.success(String.format("生成体验版成功，共处理 %d 个小程序", successAppIds.size()));

        } catch (Exception e) {
            String errorMsg = String.format("生成体验版异常，异常信息: %s", e.getMessage());
            log.error(errorMsg, e);
            return ResultDTO.fail(errorMsg);
        }
    }

    private String assemblyExtJson(String appId, String accessToken) {
        ExtJsonDto extJsonDto = new ExtJsonDto();
        extJsonDto.setExtEnable(Boolean.TRUE);
        extJsonDto.setExtAppid(appId);
        extJsonDto.setPrivacy_api_not_use(Boolean.TRUE);

        WechatQueryPluginsRequest request = new WechatQueryPluginsRequest();
        request.setAction(PluginMangeActionEnum.LIST.getAction());
        request.setPluginAppid(DouGongPluginConstant.PLUGIN_APP_ID);

        // 获取插件列表
        WechatQueryPluginsResponse wechatQueryPluginsResponse = ComponentApi.listPlugins(accessToken, request);
        if (!wechatQueryPluginsResponse.successed()) {
            log.error("appId:{}获取小程序插件列表信息异常", appId, new ProviderException("获取小程序插件列表信息异常"));
            return JSONObject.toJSONString(extJsonDto);
        }
        List<Plugin> pluginList = wechatQueryPluginsResponse.getPluginList();
        if (CollectionUtils.isEmpty(pluginList)) {
            log.info("获取小程序插件列表信息为空，暂不组装插件配置");
            return JSONObject.toJSONString(extJsonDto);
        }

        // 如果插件名称和提供者都一致，且状态都是成功，则组装插件配置
        pluginList.stream()
                .filter(el -> Objects.equals(el.getStatus(), PluginStatusEnum.APPLY_SUCCESS.getStatus()))
                .filter(el -> Objects.equals(el.getNickname(), DouGongPluginConstant.DOU_GONG_PLUGIN_NICKNAME))
                .findFirst()
                .ifPresent(el -> {
                    // 斗拱插件配置
                    Plugins plugins = new Plugins();
                    MyPlugin dgPlugin = new MyPlugin();
                    dgPlugin.setProvider(DouGongPluginConstant.PROVIDER);
                    dgPlugin.setVersion(DouGongPluginConstant.VERSION);
                    plugins.setDgBill(dgPlugin);
                    extJsonDto.setPlugins(plugins);
                });
        return JSONObject.toJSONString(extJsonDto);
    }

    @Override
    public ResultDTO setPrivacySetting(PrivateSettingVo privateSettingVo, int ver) throws Exception {
        log.info("开始设置隐私设置，appId: {}, ver: {}, privateSettingVo: {}",
                privateSettingVo != null ? privateSettingVo.getAppId() : null, ver, JSON.toJSONString(privateSettingVo));

        try {
            AssertParam.notNull(privateSettingVo, ResultDTOEnum.PARAMETER_MISSING.getCode(),
                    "privateSettingVo is null");
            AssertParam.notNull(privateSettingVo.getAppId(), ResultDTOEnum.PARAMETER_MISSING.getCode(),
                    "appId is null");

            WechatAuthorizer wechatAuthorizer = authorizerService.getAccessTokenByAppId(
                    privateSettingVo.getAppId());
            if (ObjectUtils.isEmpty(wechatAuthorizer)) {
                String errorMsg = String.format("设置隐私设置失败，appId不存在对象，appId: %s", privateSettingVo.getAppId());
                log.error(errorMsg);
                return ResultDTO.fail(errorMsg);
            }

            WxSetPrivacyReq wxSetPrivacyReq = new WxSetPrivacyReq();
            wxSetPrivacyReq.setPrivacy_ver(ver);
            wxSetPrivacyReq.setOwner_setting(privateSettingVo.getOwner_setting());
            wxSetPrivacyReq.setSetting_list(privateSettingVo.getSetting_list());

            log.info("调用微信API设置隐私设置，appId: {}, accessToken: {}, 请求参数: {}",
                    privateSettingVo.getAppId(), wechatAuthorizer.getAccessToken(), JSON.toJSONString(wxSetPrivacyReq));

            BaseResult result = ComponentApi.setPrivacySetting(wechatAuthorizer.getAccessToken(), wxSetPrivacyReq);

            log.info("微信API响应结果: {}", JSON.toJSONString(result));

            if (result == null || !result.successed()) {
                String errorMsg = String.format("设置隐私设置失败，appId: %s, errcode: %s, errmsg: %s",
                        privateSettingVo.getAppId(), result != null ? result.getErrcode() : "null",
                        result != null ? result.getErrmsg() : "响应为空");
                log.error(errorMsg);
                return ResultDTO.fail(errorMsg);
            }

            log.info("设置隐私设置成功，appId: {}", privateSettingVo.getAppId());
            return ResultDTO.success();

        } catch (Exception e) {
            String errorMsg = String.format("设置隐私设置异常，appId: %s, 异常信息: %s",
                    privateSettingVo != null ? privateSettingVo.getAppId() : "null", e.getMessage());
            log.error(errorMsg, e);
            return ResultDTO.fail(errorMsg);
        }
    }

//    public void assertSuccess(String appId, BaseResult response) throws Exception {
//        if (null == response) {
//            throw new Exception("wechat.api.call.fail,调用微信服务失败");
//        } else if (!response.successed()) {
//            throw new Exception(response.getErrcode() + Optional.ofNullable(response.getErrmsg()).orElse(String.valueOf(response.getErrcode())));
//        }
//    }

    @Override
    public ResultDTO submitAuditPackage(CommitAuditVo commitAuditVo) throws Exception {
        log.info("开始提交审核，请求参数: {}", JSON.toJSONString(commitAuditVo));

        try {
            List<Integer> devList = new ArrayList<>();
            devList.add(VersionStatus.DEV_VERSION.getCode());
            devList.add(VersionStatus.AUDIT_UNPASS.getCode());
            List<WechatAuthorizer> authorizers = new ArrayList<>();

            if (!StringUtils.isEmpty(commitAuditVo.getAppId())) {
                List<WechatTemplatePackage> templatePackages = templatePackageMapper.selectPackageListByStatus(
                        commitAuditVo.getAppId(), devList);
                if (CollectionUtils.isEmpty(templatePackages)) {
                    String errorMsg = String.format("提交审核失败，appId: %s 非开发版本或者失败版本不可提交审核", commitAuditVo.getAppId());
                    log.error(errorMsg);
                    return ResultDTO.fail(errorMsg);
                }

                WechatAuthorizer wechatAuthorizer = authorizerService.getAccessTokenByAppId(
                        commitAuditVo.getAppId());
                if (ObjectUtils.isEmpty(wechatAuthorizer)) {
                    String errorMsg = String.format("提交审核失败，appId: %s 不存在授权信息", commitAuditVo.getAppId());
                    log.error(errorMsg);
                    return ResultDTO.fail(errorMsg);
                }
                authorizers.add(wechatAuthorizer);
            } else {
                List<WechatTemplatePackage> templatePackages = templatePackageMapper.selectPagListByStatus(
                        dictSysParameter.getParameter().getTpAppId(), devList);
                if (!CollectionUtils.isEmpty(templatePackages)) {
                    List<String> appIds = new ArrayList<>();
                    templatePackages.forEach(templatePackage -> {
                        appIds.add(templatePackage.getAppid());
                    });
                    authorizers = authorizerService.getAuthorizers(appIds);
                } else {
                    String errorMsg = "全量提交审核失败：暂无可提交审核的小程序";
                    log.error(errorMsg);
                    return ResultDTO.fail(errorMsg);
                }
            }
            List<Integer> auditList = new ArrayList<>();
            auditList.add(VersionStatus.AUDIT_ING.getCode());
            auditList.add(VersionStatus.AUDIT_PASS.getCode());
            auditList.add(VersionStatus.AUDIT_UNPASS.getCode());
            auditList.add(VersionStatus.AUDIT_DELAY.getCode());

            List<Integer> continueList = new ArrayList<>();
            continueList.add(VersionStatus.ONLINE_VERSION.getCode());
            continueList.add(VersionStatus.AUDIT_ING.getCode());
            continueList.add(VersionStatus.AUDIT_PASS.getCode());

            String testPhone = systemParametersService.selectByKey(
                    SysParamKeyEnum.PRODUCT_TEST_PHONE.getKey());
            String testCode = systemParametersService.selectByKey(
                    SysParamKeyEnum.PRODUCT_TEST_CODE.getKey());

            SubmitAudit submitAudit = new SubmitAudit();
            submitAudit.setItem_list(commitAuditVo.getItemList());
            submitAudit.setOrder_path(ORDER_PATH);
            SubmitAudit.PreviewInfo previewInfo = new SubmitAudit.PreviewInfo();
            previewInfo.setPic_id_list(commitAuditVo.getPicIdList());
            previewInfo.setVideo_id_list(commitAuditVo.getVideoIdList());
            submitAudit.setPreview_info(previewInfo);
            submitAudit.setVersion_desc(
                    "小程序主要提供了电商交易平台服务，小程序登录体验账号：" + testPhone + " 验证码："
                            + testCode);
            submitAudit.setFeedback_info(
                    "该小程序展示的商品为正式运营商品，登录体验账号：" + testPhone + "验证码：" + testCode);

            log.info("提交审核请求参数: {}", JSON.toJSONString(submitAudit));

        for (WechatAuthorizer authorizer : authorizers) {
            log.info("提交审核的appId appId={}", authorizer.getAppId());
            //判断当前体验版的模板Id和线上的模板Id或者审核中、审核成功的模板Id一样就不需要提交审核
            Boolean outFlag = false;
            WechatTemplatePackage templatePackages = templatePackageMapper.selectPackageByStatus(
                    authorizer.getAppId(), VersionStatus.DEV_VERSION.getCode());
            if (!ObjectUtils.isEmpty(templatePackages)) {
                List<WechatTemplatePackage> continuePackages = templatePackageMapper.selectPackageListByStatus(
                        authorizer.getAppId(), continueList);
                for (WechatTemplatePackage e : continuePackages) {
                    if (templatePackages.getTemplateId().intValue() == e.getTemplateId()) {
                        outFlag = true;
                    }
                }
                if (outFlag) {
                    continue;
                }
            }
            log.info("提交审核信息：{}", submitAudit.toString());
            SubmitAuditResult submitAuditResult = WxaApi.submitAudit(authorizer.getAccessToken(),
                    submitAudit);
            if (!submitAuditResult.successed()) {
                log.info("微信返回有误,errMsg=" + submitAuditResult.getErrmsg());
                continue;
            }
            //此处删除所有老的审核版本，包括审核中，审核失败，审核延迟，审核通过
            List<WechatTemplatePackage> auditPackages = templatePackageMapper.selectPackageListByStatus(
                    authorizer.getAppId(), auditList);
            auditPackages.forEach(pack -> {
                templatePackageMapper.deleteByPrimaryKey(pack.getId());
            });
            //将最近的体验版本设置为审核版本
            if (ObjectUtils.isEmpty(templatePackages)) {
                log.info("未找到开发版本,appId=" + authorizer.getAppId());
                continue;
            }
            templatePackages.setPkgStatus(VersionStatus.AUDIT_ING.getCode());
            templatePackages.setAuditId(submitAuditResult.getAuditid());
            templatePackages.setUpdateTime(LocalDateTime.now());
            templatePackageMapper.updateByPrimaryKeySelective(templatePackages);
        }
        log.info("***********************提交审核结束***********************");
        return ResultDTO.success();
    }

    @Override
    public ResultDTO<?> applySetOrderPath(List<String> appIds) {
        if (CollectionUtils.isEmpty(appIds)) {
            return ResultDTO.success();
        }
        if (appIds.size() > 100) {
            return ResultDTO.fail("最多批量设置100个小程序");
        }
        // 调用微信接口
        // 获取component access token
        String tpAccessTokenJsonString = dictSysParameter.getParameter().getTpAccessTokenJson();
        JsonObject obj = JsonParser.parseString(tpAccessTokenJsonString).getAsJsonObject();
        String tpAccessToken = obj.get("accessToken").getAsString();

        // 获取测试手机号和验证码
        String phone = systemParametersService.selectByKey(SysParamKeyEnum.PRODUCT_TEST_PHONE.getKey());
        String code = systemParametersService.selectByKey(SysParamKeyEnum.PRODUCT_TEST_CODE.getKey());

        OrderPathBatchReq batchReq = OrderPathBatchReq.builder()
                .path(ORDER_PATH)
                .video(ORDER_PATH_VIDEO_URL)
                .appid_list(appIds)
                .test_account(phone)
                .test_pwd(code)
                .build();

        log.info("设定订单页path, appIds: {}", JSON.toJSONString(appIds));
        BaseResult baseResult = WxaApi.applySetOrderPath(tpAccessToken, batchReq);
        if (!baseResult.successed()) {
            log.info("设定订单页path失败,error code: {}, errMsg: {}", baseResult.hashCode(), baseResult.getErrmsg());
            return ResultDTO.fail(baseResult.getErrmsg());
        }
        log.info("***********************订单页path设定结束***********************");
        return ResultDTO.success();
    }

    @Override
    public ResultDTO releasePackage(CommitAuditVo commitAuditVo) throws Exception {
        List<WechatAuthorizer> wechatAuthorizers = new ArrayList<>();
        List<Integer> passList = new ArrayList<>();
        passList.add(VersionStatus.AUDIT_PASS.getCode());
        if (!ObjectUtils.isEmpty(commitAuditVo) && !StringUtils.isEmpty(commitAuditVo.getAppId())) {
            WechatTemplatePackage templatePackages = templatePackageMapper.selectPackageByStatus(
                    commitAuditVo.getAppId(), VersionStatus.AUDIT_PASS.getCode());
            if (ObjectUtils.isEmpty(templatePackages)) {
                return ResultDTO.fail(commitAuditVo.getAppId() + "没有审核通过的记录");
            }

            WechatAuthorizer wechatAuthorizer = authorizerService.getAccessTokenByAppId(
                    commitAuditVo.getAppId());
            wechatAuthorizers.add(wechatAuthorizer);
        } else {
            wechatAuthorizers = wechatAuthorizerMapper.getAuthTenants(
                    dictSysParameter.getParameter().getTpAppId());
            List<WechatTemplatePackage> templatePackages = templatePackageMapper.selectPagListByStatus(
                    dictSysParameter.getParameter().getTpAppId(), passList);
            if (wechatAuthorizers.size() != templatePackages.size()) {
                return ResultDTO.fail("全量发布：有小程序没有通过审核");
            }
        }
        wechatAuthorizers.forEach(authorizer -> {
            log.info("发布的appId appId={}", authorizer.getAppId());
            WxaApi.release(authorizer.getAccessToken());
            //发布前设置小程序域名
            try {
                setModifyDomain(authorizer.getAppId());
                //setModifyWebviewDomain(authorizer.getAppId());
            } catch (Exception e) {
                log.warn("发布前设置小程序域名失败, msg = {}", e.getMessage(), e);
            }
            //查询当前审核通过的记录
            WechatTemplatePackage passPackages = templatePackageMapper.selectPackageByStatus(
                    authorizer.getAppId(), VersionStatus.AUDIT_PASS.getCode());
            //查询是否存在上一个生产环境版本
            WechatTemplatePackage curProdPackages = templatePackageMapper.selectPackageByStatus(
                    authorizer.getAppId(), VersionStatus.ONLINE_VERSION.getCode());
            //更新当前的审核通过的版本为生产环境版本
            passPackages.setPkgStatus(VersionStatus.ONLINE_VERSION.getCode());
            templatePackageMapper.updateByPrimaryKeySelective(passPackages);
            //更新配置表关联的线上版本id
            WechatLiteConfig wechatLiteConfig = wechatLiteConfigMapper.selectByAppId(
                    authorizer.getAppId());
            wechatLiteConfig.setOnlineId(passPackages.getId());
            wechatLiteConfigMapper.updateByPrimaryKeySelective(wechatLiteConfig);
            //如果存在上个生产环境版本则把生产环境版本设置为当前的上个版本
            if (null != curProdPackages) {
                curProdPackages.setPkgStatus(VersionStatus.PRE_VERSION.getCode());
                templatePackageMapper.updateByPrimaryKeySelective(curProdPackages);
            }
        });
        log.info("***********************发布结束***********************");
        return ResultDTO.success();
    }

    @Override
    public ResultDTO rollbackPackage(CommitAuditVo commitAuditVo) throws Exception {
        List<WechatAuthorizer> authorizers = new ArrayList<>();
        List<Integer> onlineList = new ArrayList<>();
        onlineList.add(VersionStatus.ONLINE_VERSION.getCode());

        if (!ObjectUtils.isEmpty(commitAuditVo) && !StringUtils.isEmpty(commitAuditVo.getAppId())) {
            WechatTemplatePackage templatePackages = templatePackageMapper.selectPackageByStatus(
                    commitAuditVo.getAppId(), VersionStatus.ONLINE_VERSION.getCode());
            if (ObjectUtils.isEmpty(templatePackages)) {
                return ResultDTO.fail(commitAuditVo.getAppId() + "没有可回退的版本");
            }

            WechatAuthorizer wechatAuthorizer = authorizerService.getAccessTokenByAppId(
                    commitAuditVo.getAppId());
            authorizers.add(wechatAuthorizer);
        } else {
            List<WechatTemplatePackage> templatePackages = templatePackageMapper.selectPagListByStatus(
                    dictSysParameter.getParameter().getTpAppId(), onlineList);
            if (!CollectionUtils.isEmpty(templatePackages)) {
                List<String> appIds = new ArrayList<>();
                templatePackages.forEach(templatePackage -> {
                    appIds.add(templatePackage.getAppid());
                });
                authorizers = authorizerService.getAuthorizers(appIds);
            } else {
                return ResultDTO.fail(commitAuditVo.getAppId() + "没有可回退的版本");
            }
        }
        authorizers.forEach(authorizer -> {
            log.info("版本退回的appId appId={}", authorizer.getAppId());
            WxaApi.revertcoderelease(authorizer.getAccessToken());
            //删除当前版本信息
            WechatTemplatePackage passPackages = templatePackageMapper.selectPackageByStatus(
                    authorizer.getAppId(), VersionStatus.ONLINE_VERSION.getCode());
            passPackages.setPkgStatus(VersionStatus.OTHER.getCode());
            templatePackageMapper.updateByPrimaryKeySelective(passPackages);
            //如果有上个版本信息则把上个版本信息回滚到当前版本去
            WechatTemplatePackage curProdPackages = templatePackageMapper.selectPackageByStatus(
                    authorizer.getAppId(), VersionStatus.PRE_VERSION.getCode());
            if (!ObjectUtils.isEmpty(curProdPackages)) {
                curProdPackages.setPkgStatus(VersionStatus.ONLINE_VERSION.getCode());
                templatePackageMapper.updateByPrimaryKeySelective(curProdPackages);
                //更新配置表关联关系
                WechatLiteConfig wechatLiteConfig = wechatLiteConfigMapper.selectByAppId(
                        authorizer.getAppId());
                wechatLiteConfig.setOnlineId(curProdPackages.getId());
                wechatLiteConfigMapper.updateByPrimaryKeySelective(wechatLiteConfig);
            }
        });
        log.info("***********************版本退回结束***********************");
        return ResultDTO.success();
    }

    @Override
    public ResultDTO withdrawPackage(CommitAuditVo commitAuditVo) throws Exception {
        List<WechatAuthorizer> authorizers = new ArrayList<>();
        List<Integer> auditList = new ArrayList<>();
        auditList.add(VersionStatus.AUDIT_ING.getCode());
        auditList.add(VersionStatus.AUDIT_UNPASS.getCode());
        auditList.add(VersionStatus.AUDIT_PASS.getCode());
        auditList.add(VersionStatus.AUDIT_DELAY.getCode());
        if (!ObjectUtils.isEmpty(commitAuditVo) && !StringUtils.isEmpty(commitAuditVo.getAppId())) {
            List<WechatTemplatePackage> templatePackages = templatePackageMapper.selectPackageListByStatus(
                    commitAuditVo.getAppId(), auditList);
            if (CollectionUtils.isEmpty(templatePackages)) {
                return ResultDTO.fail(commitAuditVo.getAppId() + "没有审核相关的记录");
            }

            WechatAuthorizer wechatAuthorizer = authorizerService.getAccessTokenByAppId(
                    commitAuditVo.getAppId());
            authorizers.add(wechatAuthorizer);
        } else {
            List<WechatTemplatePackage> templatePackages = templatePackageMapper.selectPagListByStatus(
                    dictSysParameter.getParameter().getTpAppId(), auditList);
            if (!CollectionUtils.isEmpty(templatePackages)) {
                List<String> appIds = new ArrayList<>();
                templatePackages.forEach(templatePackage -> {
                    appIds.add(templatePackage.getAppid());
                });
                authorizers = authorizerService.getAuthorizers(appIds);
            } else {
                return ResultDTO.fail("全量审核撤回:没有可撤回的版本");
            }
        }
        authorizers.forEach(info -> {
            log.info("审核撤回的appId appId={}", info.getAppId());
            WxaApi.undocodeaudit(info.getAccessToken());
            //设置已经过审核和审核中或者被拒绝的版本为已撤回
            List<WechatTemplatePackage> templatePackages = templatePackageMapper.selectPackageListByStatus(
                    info.getAppId(), auditList);
            WechatTemplatePackage templatePackage = templatePackages.get(
                    LiteConfigPublicConsts.ONLINE_RETAILERS_OFF.getCode());
            templatePackage.setPkgStatus(VersionStatus.AUDIT_UNDO.getCode());
            templatePackageMapper.updateByPrimaryKeySelective(templatePackage);
        });
        log.info("***********************审核撤回结束***********************");
        return ResultDTO.success();
    }

    @Override
    public ResultDTO setModifyDomain(String appId) throws Exception {
        log.info("开始设置小程序域名，appId: {}", appId);

        try {
            AssertParam.hasText(appId, "缺少参数appId");

            WechatAuthorizer wechatAuthorizer = wechatAuthorizerMapper.selectOneByAppId(appId,
                    dictSysParameter.getParameter().getTpAppId());
            if (ObjectUtils.isEmpty(wechatAuthorizer)) {
                String errorMsg = String.format("设置小程序域名失败，appId不存在对象，appId: %s", appId);
                log.error(errorMsg);
                return ResultDTO.fail(errorMsg);
            }

            String downloadDomain = dictSysParameter.getParameter().getLiteRequestDownloadDomain();
            String requestDomain = dictSysParameter.getParameter().getLiteRequestRequestDomain();
            String socketDomain = dictSysParameter.getParameter().getLiteRequestSocketDomain();
            String uploadDomain = dictSysParameter.getParameter().getLiteRequestUploadDomain();

            log.info("调用设置小程序域名，appId: {}, accessToken: {}, downloadDomain: {}, requestDomain: {}, socketDomain: {}, uploadDomain: {}",
                    appId, wechatAuthorizer.getAccessToken(), downloadDomain, requestDomain, socketDomain, uploadDomain);

            BaseResult result = authorizerService.setModifyDomain(wechatAuthorizer.getAccessToken(),
                    WxDomainEnum.SET.getKey(),
                    downloadDomain,
                    requestDomain,
                    socketDomain,
                    uploadDomain);

            log.info("设置小程序域名API响应结果: {}", JSON.toJSONString(result));

            if (result == null || !result.successed()) {
                String errorMsg = String.format("设置小程序域名失败，appId: %s, errcode: %s, errmsg: %s",
                        appId, result != null ? result.getErrcode() : "null",
                        result != null ? result.getErrmsg() : "响应为空");
                log.error(errorMsg);
                return ResultDTO.fail(errorMsg);
            }

            log.info("设置小程序域名成功，appId: {}", appId);
            return ResultDTO.success();

        } catch (Exception e) {
            String errorMsg = String.format("设置小程序域名异常，appId: %s, 异常信息: %s", appId, e.getMessage());
            log.error(errorMsg, e);
            return ResultDTO.fail(errorMsg);
        }
    }

//    @Override
//    public void setModifyWebviewDomain(String appId) throws Exception {
//        Assert.hasText(appId, "缺少参数appId");
//        WechatAuthorizer wechatAuthorizer = wechatAuthorizerMapper.selectOneByAppId(appId);
//        authorizerService.setModifyWebviewDomain(wechatAuthorizer.getAccessToken(), WxDomainEnum.SET.getKey(), dictSysParameter.getParameter(SysParamKey.LITE_WEBVIEW_WEB_VIEW_DOMAIN.getKey(), ""));
//    }

    /**
     * 暂时没有用到，后续可能会使用
     *
     * @param appId
     * @throws Exception
     */
    @Override
    public ResultDTO initDomain(String appId) throws Exception {
        log.info("开始初始化小程序域名，appId: {}", appId);

        try {
            ResultDTO result = setModifyDomain(appId);
            //setModifyWebviewDomain(appId);

            if (result.isFail()) {
                log.error("初始化小程序域名失败，appId: {}, 失败原因: {}", appId, result.getMessage());
                return result;
            }

            log.info("初始化小程序域名成功，appId: {}", appId);
            return ResultDTO.success();

        } catch (Exception e) {
            String errorMsg = String.format("初始化小程序域名异常，appId: %s, 异常信息: %s", appId, e.getMessage());
            log.error(errorMsg, e);
            return ResultDTO.fail(errorMsg);
        }
    }

    /**
     * 通过调用微信的接口来更新审核状态
     *
     * @return
     */
    @Override
    public List<WechatTemplatePackage> syncAuditStatus(@Nullable String appId) {
        List<WechatTemplatePackage> packageList = templatePackageMapper.selectPackageListByStatus(
                appId, Arrays.asList(VersionStatus.AUDIT_ING.getCode()));
        if (CollectionUtils.isEmpty(packageList)) {
            return packageList;
        }

        final int daysToExpire = 60;
        final LocalDateTime expiredTime = LocalDateTime.now().plus(-daysToExpire, ChronoUnit.DAYS);

        return packageList.stream().filter(templatePackage -> {
            if (null != templatePackage.getCreateTime() && expiredTime.isAfter(
                    templatePackage.getCreateTime())) {
                //已经超过60天就直接设置为审核不通过；
                int updated = templatePackageMapper.updateByPrimaryKeySelective(
                        new WechatTemplatePackage().setId(templatePackage.getId())
                                .setPkgStatus(VersionStatus.AUDIT_UNPASS.getCode()).setRemark(
                                        String.format("超过%d天未能从微信处同步审核状态，自动设置为不通过",
                                                daysToExpire)));
                return updated == 1;
            }
            WechatAuthorizer wechatAuthorizer = authorizerService.getAccessTokenByAppId(
                    templatePackage.getAppid());
            if (null == wechatAuthorizer
                    || StringUtils.isBlank(wechatAuthorizer.getAccessToken())
                    || StringUtils.isBlank(templatePackage.getAuditId())
            ) {
                log.warn("unable to sync app audit status:{}, wechatAuthorizer:{}",
                        JSON.toJSONString(templatePackage), wechatAuthorizer);
                return false;
            }
            //0	审核成功
            //1	审核被拒绝
            //2	审核中
            //3	已撤回
            //4	审核延后
            GetAuditstatusResult auditstatusResult = WxaApi.getAuditStatusResult(
                    wechatAuthorizer.getAccessToken(), templatePackage.getAuditId());
            if (null == auditstatusResult || !auditstatusResult.successed()
                    || null == auditstatusResult.getStatus()) {
                log.warn("unable to get audit status:{}, {}", auditstatusResult, templatePackage);
                return false;
            }
            log.info("app:{}, auditId:{} 的审核状态:{}", appId, templatePackage.getAuditId(), auditstatusResult);
            VersionStatus newStatus = VersionStatus.fromWeixinAuditStatus(auditstatusResult.getStatus());
            if (VersionStatus.AUDIT_ING.compareTo(newStatus) == 0) {
                log.info("no update on this audit:{}, app:{}, VersionStatus:{}", templatePackage.getAuditId(), appId, newStatus);
                return false;
            }
            int updated = templatePackageMapper.updateByPrimaryKeySelective(
                    new WechatTemplatePackage().setId(templatePackage.getId())
                            .setPkgStatus(
                                    newStatus.getCode())
                            .setRemark(String.join("\n", String.valueOf(auditstatusResult.getStatus()),
                                    auditstatusResult.getReason(), auditstatusResult.getScreenshot())));
            return updated == 1;
        }).collect(Collectors.toList());
    }
}
