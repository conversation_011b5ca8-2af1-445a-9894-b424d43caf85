package com.cosfo.oms.wechat.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.oms.common.context.AppType;
import com.cosfo.oms.common.context.LiteConfigPublicConsts;
import com.cosfo.oms.common.utils.DateUtil;
import com.cosfo.oms.system.mapper.SystemParametersMapper;
import com.cosfo.oms.system.model.po.SystemWechantTpInfo;
import com.cosfo.oms.system.service.Impl.DictSysParameter;
import com.cosfo.oms.system.service.SystemWechantTpInfoService;
import com.cosfo.oms.wechat.api.ComponentApi;
import com.cosfo.oms.wechat.api.WxaApi;
import com.cosfo.oms.wechat.bean.component.ApiQueryAuthResult;
import com.cosfo.oms.wechat.bean.wxa.ModifyDomain;
import com.cosfo.oms.wechat.bean.wxa.ModifyDomainResult;
import com.cosfo.oms.wechat.mapper.WechatAuthorizerMapper;
import com.cosfo.oms.wechat.model.dto.TpAccessTokenJsonDto;
import com.cosfo.oms.wechat.model.dto.WechatAuthorizerDto;
import com.cosfo.oms.wechat.model.po.WechatAuthorizer;
import com.cosfo.oms.wechat.service.AuthorizerService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class AuthorizerServiceImpl implements AuthorizerService {
    @Resource
    SystemParametersMapper systemParametersMapper;
    @Resource
    DictSysParameter dictSysParameter;
    @Resource
    WeixinTpServiceImpl weixinTpService;
    @Resource
    WechatAuthorizerMapper wechatAuthorizerMapper;
//    @Resource
//    TenantAuthConnectionService tenantAuthConnectionService;
    @Resource
    WeixinTpServiceImpl weixinTpServiceImpl;
    @Resource
    private SystemWechantTpInfoService systemWechantTpInfoService;
//    @Resource
//    WechatLiteConfigMapper wechatLiteConfigMapper;
//    @Resource
//    WeixinTemplateService weixinTemplateService;
//    @Resource
//    private RedisUtils redisUtils;


    @Override
    public WechatAuthorizerDto getAuthorizer() {
        SystemWechantTpInfo systemWechantTpInfo = dictSysParameter.init();
        WechatAuthorizerDto wechatAuthorizerDto = new WechatAuthorizerDto();
        wechatAuthorizerDto.setAppId(systemWechantTpInfo.getTpAppId());
        wechatAuthorizerDto.setAccessSecret(systemWechantTpInfo.getTpAppSecret());
        wechatAuthorizerDto.setTicket(systemWechantTpInfo.getTpAppTicket());
        if (StringUtils.isNotBlank(systemWechantTpInfo.getTpAccessTokenJson())) {
            TpAccessTokenJsonDto tokenJsonDto = JSON.parseObject(systemWechantTpInfo.getTpAccessTokenJson(), TpAccessTokenJsonDto.class);
            wechatAuthorizerDto.setAccessToken(tokenJsonDto.getAccessToken());
            wechatAuthorizerDto.setAccessTokenExpiretime(tokenJsonDto.getAccessTokenExpiretime());
        }
        wechatAuthorizerDto.setAppType(AppType.WEIXIN_TP.getCode());
        wechatAuthorizerDto.setToken(systemWechantTpInfo.getTpToken());
        wechatAuthorizerDto.setEncodingAesKey(systemWechantTpInfo.getTpEncodingAesKey());
        return wechatAuthorizerDto;
    }

//    @Override
//    public void updateTpTicket(String appId, String ticket) {
//        SystemParameters systemParameters = systemParametersMapper.selectByTpTicket("tp_app_ticket");
//        if (ObjectUtils.isEmpty(systemParameters)) {
//            SystemParameters systemParameter = new SystemParameters();
//            systemParameter.setParamKey("tp_app_ticket");
//            systemParameter.setParamValue(ticket);
//            systemParameter.setDescription("第三方token获取票据");
//            systemParameter.setCreateTime(new Date());
//            systemParametersMapper.insertSelective(systemParameter);
//        } else {
//            SystemParameters systemParameter = new SystemParameters();
//            systemParameter.setParamValue(ticket);
//            systemParameter.setId(systemParameters.getId());
//            systemParametersMapper.updateByPrimaryKeySelective(systemParameter);
//        }
//
//    }

    @Override
    public void updateTpToken(boolean force, WechatAuthorizerDto tpAuthorizer) throws Exception {
        //component_access_token,有效期2h，设置一小时更新一次
        if (force || StringUtils.isBlank(tpAuthorizer.getAccessToken()) || tpAuthorizer.getAccessTokenExpiretime() == null || tpAuthorizer.getAccessTokenExpiretime().toInstant(ZoneOffset.ofHours(8)).toEpochMilli() - System.currentTimeMillis() < 1800000) {
            //获取调用凭证
            WechatAuthorizerDto updateAuthorizer = weixinTpService.getAuthToken(tpAuthorizer);
            if (AppType.WEIXIN_TP.getCode().equals(updateAuthorizer.getAppType())) {
                log.info("刷新Tp的token");
                TpAccessTokenJsonDto tpAccessTokenJsonDto = new TpAccessTokenJsonDto();
                tpAccessTokenJsonDto.setAppId(updateAuthorizer.getAppId());
                tpAccessTokenJsonDto.setAccessToken(updateAuthorizer.getAccessToken());
                tpAccessTokenJsonDto.setAccessTokenExpiretime(updateAuthorizer.getAccessTokenExpiretime());
                String tokenJson = JSONObject.toJSONString(tpAccessTokenJsonDto);
                SystemWechantTpInfo systemWechantTpInfo = dictSysParameter.getParameter();
                systemWechantTpInfo.setTpAccessTokenJson(tokenJson);
                systemWechantTpInfoService.updateById(systemWechantTpInfo);
            } else {
                WechatAuthorizer wechatAuthorizer = new WechatAuthorizer();
                BeanUtils.copyProperties(updateAuthorizer, wechatAuthorizer);
                wechatAuthorizerMapper.updateByPrimaryKeySelective(wechatAuthorizer);
            }
        }
    }

//    @Override
//    public WechatAuthorizer authorized(WechatAuthorizerDto wechatAuthorizerDto, String appId, String authCode, String preAuthCode) {
//        try {
//            //从缓存取出预授权码关系的tenantId
//            PreAuthCodeDto preAuthCodeDto = StringUtils.isBlank(preAuthCode) ? null : (PreAuthCodeDto) redisUtils.get(preAuthCode);
//            if (!ObjectUtils.isEmpty(preAuthCodeDto)) {
//                log.info("缓存的预授权码 preCode={}", preAuthCodeDto.getPreAuthCode());
//                log.info("缓存的预授权码 tenantId={}", preAuthCodeDto.getTenantId());
//            } else {
//                log.info("缓存的preAuthCodeDto为空");
//            }
//            //新增授权方
//            WechatAuthorizer selectAuthorizer = wechatAuthorizerMapper.selectOneByAppId(appId);
//            if (ObjectUtils.isEmpty(selectAuthorizer)) {
//                WechatAuthorizer wechatAuthorizer = new WechatAuthorizer();
//                wechatAuthorizer.setAppId(appId);
//                wechatAuthorizer.setAuthorizationCode(authCode);
//                wechatAuthorizer.setCreateTime(LocalDateTime.now());
//                wechatAuthorizerMapper.insertSelective(wechatAuthorizer);
//            } else {
//                WechatAuthorizer wechatAuthorizer = new WechatAuthorizer();
//                wechatAuthorizer.setId(selectAuthorizer.getId());
//                wechatAuthorizer.setAppId(appId);
//                wechatAuthorizer.setAuthorizationCode(authCode);
//                wechatAuthorizerMapper.updateByPrimaryKeySelective(wechatAuthorizer);
//            }
//
//            //新增授权方关联关系
//            TenantAuthConnection tenantAuthConnection = tenantAuthConnectionService.selectAuthorizer(appId);
//            if (ObjectUtils.isEmpty(tenantAuthConnection)) {
//                tenantAuthConnectionService.insertAuthorizer(appId, ObjectUtils.isNotEmpty(preAuthCodeDto) ? (preAuthCodeDto.getTenantId()) : null);
//            } else {
//                tenantAuthConnectionService.updateAuthorizer(tenantAuthConnection.getId(), appId, ObjectUtils.isNotEmpty(preAuthCodeDto) ? (preAuthCodeDto.getTenantId()) : null, LiteConfigPublicConsts.NORMAL_CONFIG_STATE.getCode());
//            }
//
//            WechatAuthorizer authorizer = this.getAuthTokenByAuthCode(wechatAuthorizerDto, appId, authCode);
//            authorizer.setAppId(appId);
//            authorizer.setAuthorizationCode(authCode);
//            authorizer.setStatus(LiteConfigPublicConsts.NORMAL_CONFIG_STATE.getCode());
//            authorizer.setAppType(AppType.WEIXIN_LITE.getCode());
//            wechatAuthorizerMapper.updateByAppId(authorizer);
//            //新增授权方基本信息
//            this.updateAuthorizerInfo(wechatAuthorizerDto, appId);
//            return authorizer;
//        } catch (Exception e) {
//            log.error(e.getMessage(), e);
//            return null;
//        }
//    }

    @Override
    public void refreshAuthorizerToken(boolean force, String appId) throws Exception {
        this.refreshToken(force, appId);
    }

    @Override
    public void refreshToken(boolean force, String appId) throws Exception {
        log.info("刷新token的appId appId={}", appId);
        //微信app的token刷新
        List<WechatAuthorizer> authorizerBaseInfoList = wechatAuthorizerMapper.selectByRefreshToken(appId, LiteConfigPublicConsts.NORMAL_CONFIG_STATE.getCode(),dictSysParameter.getParameter().getTpAppId());
        WechatAuthorizerDto wechatAuthorizerDto = this.getAuthorizer();
        if (StringUtils.isBlank(appId) || appId.equals(wechatAuthorizerDto.getAppId())) {
            updateTpToken(force, wechatAuthorizerDto);
        }
        for (WechatAuthorizer record : authorizerBaseInfoList) {
            try {
                //微信直接对接
                List<LocalDateTime> expireTimeList = new ArrayList();
                expireTimeList.add(Optional.ofNullable(record.getAccessTokenExpiretime()).orElse(LocalDateTime.now()));
                LocalDateTime earliestExpireTime = Collections.min(expireTimeList);
                if (force || (DateUtil.localDateToStamp(earliestExpireTime) - System.currentTimeMillis()) < 600000) {
                    log.info("刷新Authorizer的appId appId={}", record.getAppId());
                    WechatAuthorizerDto authorizerDto = new WechatAuthorizerDto();
                    BeanUtils.copyProperties(record, authorizerDto);
                    authorizerDto = weixinTpService.getAuthToken(authorizerDto);
                    WechatAuthorizer authorizerInfo = new WechatAuthorizer();
                    BeanUtils.copyProperties(authorizerDto, authorizerInfo);
                    authorizerInfo.setUpdateTime(LocalDateTime.now());
                    wechatAuthorizerMapper.updateByPrimaryKeySelective(authorizerInfo);
                }
            } catch (Exception e) {
                log.error("{},{}", record.getAppId(), e.getMessage(), e);
            }
        }
    }

    //获取授权方刷新码
    @Override
    public WechatAuthorizer getAuthInfo(String appId, String authCode) throws Exception {
        log.info("刷新token的appId appId={}", appId);
        WechatAuthorizerDto componentAuthorizer = this.getAuthorizer();
        WechatAuthorizer wechatAuthorizer = this.getAuthTokenByAuthCode(componentAuthorizer, appId, authCode);
        wechatAuthorizer.setAppId(appId);
        wechatAuthorizer.setAuthorizationCode(authCode);
        wechatAuthorizer.setStatus(LiteConfigPublicConsts.NORMAL_CONFIG_STATE.getCode());
        wechatAuthorizerMapper.updateByAppId(wechatAuthorizer);
        return wechatAuthorizer;
    }

//    @Override
//    public void unAuthorized(String appId) {
//        WechatAuthorizer selectAuthorizer = wechatAuthorizerMapper.selectByAppId(appId);
//        if (!ObjectUtils.isEmpty(selectAuthorizer)) {
//            selectAuthorizer.setStatus(LiteConfigPublicConsts.ONLINE_RETAILERS_OFF.getCode());
//            wechatAuthorizerMapper.updateByPrimaryKeySelective(selectAuthorizer);
//            TenantAuthConnection tenantAuthConnection = tenantAuthConnectionService.selectAuthorizer(appId);
//            if (!ObjectUtils.isEmpty(tenantAuthConnection)) {
//                tenantAuthConnection.setStatus(LiteConfigPublicConsts.ONLINE_RETAILERS_OFF.getCode());
//                tenantAuthConnectionService.updateAuthorizerStatus(tenantAuthConnection);
//            }
//        }
//    }

    @Override
    public WechatAuthorizer getAccessTokenByAppId(String appId) {
        WechatAuthorizer wechatAuthorizer = wechatAuthorizerMapper.selectByAppId(appId, dictSysParameter.getParameter().getTpAppId());
        return wechatAuthorizer;
    }

    @Override
    public List<WechatAuthorizer> getAuthorizers(List<String> appIds) {
        List<WechatAuthorizer> wechatAuthorizers = wechatAuthorizerMapper.selectListAppIds(appIds);
        return wechatAuthorizers;
    }


    /**
     * 获取授权信息
     *
     * @return
     * @
     */
    public WechatAuthorizer getAuthTokenByAuthCode(WechatAuthorizerDto tpAuthorizer, String appId, String authCode) throws Exception {

        ApiQueryAuthResult queryAuthResp = ComponentApi.api_query_auth(tpAuthorizer.getAccessToken(), tpAuthorizer.getAppId(), authCode);
        weixinTpServiceImpl.assertSuccess(StringUtils.isNotBlank(tpAuthorizer.getAccessToken()) ? tpAuthorizer.getAppId() : appId, queryAuthResp);

        WechatAuthorizer result = new WechatAuthorizer();
        result.setAppId(appId);
        if (queryAuthResp.getAuthorization_info() != null) {
            result.setAccessToken(queryAuthResp.getAuthorization_info().getAuthorizer_access_token());
            result.setAccessTokenExpiretime(DateUtil.stampToLocalDate(System.currentTimeMillis() + queryAuthResp.getAuthorization_info().getExpires_in() * 1000));
            result.setRefreshToken(queryAuthResp.getAuthorization_info().getAuthorizer_refresh_token());
            if (queryAuthResp.getAuthorization_info().getFunc_info() != null) {
                List<Integer> funcList = new ArrayList<>();
                for (ApiQueryAuthResult.FuncInfo funcInfo : queryAuthResp.getAuthorization_info().getFunc_info()) {
                    funcList.add(funcInfo.getFuncscope_category().getId());
                }
                Collections.sort(funcList);
                result.setFuncInfo(JSON.toJSONString(funcList));
            }
        }
        return result;
    }

//    @Override
//    public WechatLiteConfig updateAuthorizerInfo(WechatAuthorizerDto wechatAuthorizerDto, String appId) throws Exception {
//        WechatLiteConfig wechatLiteConfig = new WechatLiteConfig();
//        GetAuthorizerInfoResp getAuthorizerInfoResp = ComponentApi.api_get_authorizer_info(wechatAuthorizerDto.getAccessToken(), wechatAuthorizerDto.getAppId(), appId);
//        weixinTpServiceImpl.assertSuccess(StringUtils.isNotBlank(wechatAuthorizerDto.getAccessToken()) ? wechatAuthorizerDto.getAppId() : appId, getAuthorizerInfoResp);
//        //这块判断是不是小程序
//        if (null != getAuthorizerInfoResp.getAuthorizer_info().getMiniProgramInfo()) {
//            //如果是小程序则读取账号信息入库
//            wechatLiteConfig.setAppid(appId);
//            wechatLiteConfig.setName(getAuthorizerInfoResp.getAuthorizer_info().getNick_name());
//            wechatLiteConfig.setHeadImg(getAuthorizerInfoResp.getAuthorizer_info().getHead_img());
//            wechatLiteConfig.setOriginalId(getAuthorizerInfoResp.getAuthorizer_info().getUser_name());
//            wechatLiteConfig.setPrincipalName(getAuthorizerInfoResp.getAuthorizer_info().getPrincipal_name());
//            wechatLiteConfig.setVerify(getAuthorizerInfoResp.getAuthorizer_info().getVerify_type_info().getId());
//            String ids = getAuthorizerInfoResp.getAuthorization_info().getFunc_info().stream().map(item -> item.getFuncscope_category().getId().toString()).collect(Collectors.joining(","));
//            wechatLiteConfig.setAuthIds(ids);
//            wechatLiteConfig.setStatus(LiteConfigPublicConsts.NORMAL_CONFIG_STATE.getCode());
//
//            WechatLiteConfig liteConfig = wechatLiteConfigMapper.selectByAppId(appId);
//            if (ObjectUtils.isEmpty(liteConfig)) {
//                wechatLiteConfigMapper.insertSelective(wechatLiteConfig);
//            } else {
//                wechatLiteConfigMapper.updateByPrimaryKeySelective(wechatLiteConfig);
//            }
//            //初始化请求域名和业务域名
//            weixinTemplateService.setModifyDomain(appId);
//            //weixinTemplateService.setModifyWebviewDomain(appId);
//        }
//        return wechatLiteConfig;
//    }
//
//    @Override
//    public ResultDTO getAuthorizedList(Long tenantId) {
//        Assert.notNull(tenantId, "tenantId不能为空");
//        List<AuthorizedVo> authorizedVos = wechatLiteConfigMapper.selectAuthorizedList(tenantId);
//        return ResultDTO.success(authorizedVos);
//    }

    @Override
    public void setModifyDomain(String access_token, String action, String download_domain, String request_domain, String socket_domain, String upload_domain) throws Exception {
        ModifyDomain wxModifyDomainReq = new ModifyDomain();
        wxModifyDomainReq.setAction(action);
        if (null != download_domain) {
            wxModifyDomainReq.setDownloaddomain(download_domain.split(","));
        }
        if (null != request_domain) {
            wxModifyDomainReq.setRequestdomain(request_domain.split(","));
        }
        if (null != socket_domain) {
            wxModifyDomainReq.setWsrequestdomain(socket_domain.split(","));
        }
        if (null != upload_domain) {
            wxModifyDomainReq.setUploaddomain(upload_domain.split(","));
        }
        ModifyDomainResult response = WxaApi.modify_domain(access_token, wxModifyDomainReq);
        weixinTpServiceImpl.assertSuccess(null, response);
    }
//
//    @Override
//    public void setModifyWebviewDomain(String access_token, String action, String web_view_domain) throws Exception {
//        SetWebviewDomain setWebviewDomain = new SetWebviewDomain();
//        setWebviewDomain.setAction(action);
//        if (null != web_view_domain) {
//            setWebviewDomain.setWebviewdomain(web_view_domain.split(","));
//        }
//
//        BaseResult response = WxaApi.setwebviewdomain(access_token, setWebviewDomain);
//        weixinTpServiceImpl.assertSuccess(null, response);
//    }
}
