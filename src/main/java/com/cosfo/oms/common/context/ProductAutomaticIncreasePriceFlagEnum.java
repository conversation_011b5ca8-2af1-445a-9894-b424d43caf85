package com.cosfo.oms.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * desc:商品自动加价枚举
 * <AUTHOR>
 * @date 2022-12-10
 */
@Getter
@AllArgsConstructor
public enum ProductAutomaticIncreasePriceFlagEnum {

    /**
     * 关闭
     */
    CLOSE(0, "关闭"),
    /**
     * 开启
     */
    OPEN(1, "开启");

    /**
     * flag
     */
    private Integer flag;

    /**
     * 描述
     */
    private String desc;
}
