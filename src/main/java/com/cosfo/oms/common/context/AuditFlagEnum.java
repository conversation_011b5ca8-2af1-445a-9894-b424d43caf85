package com.cosfo.oms.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description
 * @date 2022/8/26 9:31
 */
@Getter
@AllArgsConstructor
public enum AuditFlagEnum {

    /**
     * 审核失败
     */
    AUDIT_FAIL(0, "审核失败"),

    /**
     * 审核成功
     */
    AUDIT_SUCCESS(1, "审核成功");

    /**
     * flag
     */
    private Integer flag;

    /**
     * 描述
     */
    private String desc;
}
