package com.cosfo.oms.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/6/16
 */
@Getter
@AllArgsConstructor
public enum UserStatusEnum {
    /**
     * 0-审核中
     */
    IN_AUDIT(0, "审核中"),
    /**
     * 1-审核成功
     */
    AUDIT_SUCCESS(1, "审核成功"),
    /**
     * 2-审核失败
     */
    AUDIT_FAIL(2, "审核失败");

    /**
     * 状态编码
     */
    private Integer code;
    /**
     * 状态描述
     */
    private String desc;
}
