package com.cosfo.oms.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 承担配送动作的人 的类型
 */
@Getter
@AllArgsConstructor
public enum DeliveryUndertakerTypeEnum {
    ERROR(-1, "错误"),
    /**
     * 品牌方配送
     */
    BRAND_DELIVERY(0, "品牌方配送"),

    /**
     * 三方配送
     */
    THIRD_DELIVERY(1, "三方配送");

    /**
     * 状态编码
     */
    private Integer code;
    /**
     * 状态描述
     */
    private String desc;

    public static DeliveryUndertakerTypeEnum getByCode(Integer code) {
        for (DeliveryUndertakerTypeEnum deliveryUndertakerTypeEnum : DeliveryUndertakerTypeEnum.values()) {
            if (deliveryUndertakerTypeEnum.code.equals(code)) {
                return deliveryUndertakerTypeEnum;
            }
        }
        return ERROR;
    }
}
