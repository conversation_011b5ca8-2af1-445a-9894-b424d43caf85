package com.cosfo.oms.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description
 * @date 2022/8/19 9:45
 */
@Getter
@AllArgsConstructor
public enum BalanceAuthorityTypeEnum {

    /**
     * 关闭余额权限
     */
    CLOSE_BALANCE_AUTH(0, "关闭"),

    /**
     * 开通余额权限
     */
    OPEN_BALANCE_AUTH(1, "开启");

    /**
     * 类型
     */
    private Integer type;

    /**
     * 描述
     */
    private String desc;

    public static BalanceAuthorityTypeEnum getByType(Integer type) {
        for (BalanceAuthorityTypeEnum balanceAuthorityEnum : BalanceAuthorityTypeEnum.values()) {
            if (balanceAuthorityEnum.getType().equals(type)) {
                return balanceAuthorityEnum;
            }
        }
        return null;
    }
}
