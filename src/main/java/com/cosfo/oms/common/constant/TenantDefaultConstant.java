package com.cosfo.oms.common.constant;

import java.math.BigDecimal;

/**
 * @author: monna.chen
 * @Date: 2023/3/28 15:40
 * @Description:
 */
public class TenantDefaultConstant {

    // *********** 新增品牌商城  start  *********** //
    /**
     * 默认租户类型：入驻商户
     */
    public static final Integer DEFAULT_TYPE = 0;
    /**
     * 默认租户状态：启用
     */
    public static final Integer DEFAULT_STATUS = 1;
    /**
     * 默认分账开关：关闭
     */
    public static final Integer DEFAULT_SHARING_SWITCH = 0;
    /**
     * 无仓类型
     */
    public static final Integer PROPRIETARY_TYPE = 0;
    /**
     * 三方仓类型
     */
    public static final Integer THREE_PARTIES_TYPE = 1;
    /**
     * 自营仓类型
     */
    public static final Integer SELF_WAREHOUSE_TYPE = 2;
    /**
     * 运费规则时用到的默认金额/费用
     */
    public static final BigDecimal DELIVERY_DEFAULT_FEE = BigDecimal.ZERO;
    /**
     * 运费规则类型：每单
     */
    public static final Integer ORDER_RULE_TYPE = 0;
    /**
     * 运费规则类型：每天
     */
    public static final Integer DAY_RULE_TYPE = 1;
    /**
     * 运费规则的默认价格类型:固定
     */
    public static final Integer DEFAULT_PRICE_TYPE = 0;
    /**
     * 品牌方运费规则默认类型:采用鲜沐运费
     */
    public static final Integer TENANT_DELIVERY_TYPE = 0;
    /**
     * 默认分组
     */
    public static final String DEFAULT_GROUP = "默认分组";
    /**
     * 分组类型 1-默认分组
     */
    public static final Integer GROUP_TYPE = 1;
    /**
     * 代仓费用：规则类型。默认 1-按件数
     */
    public static final Integer RULE_TYPE = 1;
    /**
     * 代仓费用：规则
     */
    public static final String RULE = "[{\"amount\":0,\"count\":1}]";
    /**
     * 自动加价标识。默认 0-关闭
     */
    public static final Integer PRICE_FLAG = 0;
    /**
     * 订单售后规则  默认类型:配送仓库
     */
    public static final Integer AFTER_SALE_TYPE = 0;
    /**
     * 订单售后规则 默认类型 0-默认
     */
    public static final Integer AFTER_SALE_FLAG_DEFAULT = 0;
    /**
     * 订单售后规则 默认类型 1-非默认
     */
    public static final Integer AFTER_SALE_FLAG_DEFAULT_NON = 1;
    /**
     * 订单售后规则 默认规则的规则内容
     */
    public static final String AFTER_SALE_DEFAULT_RULE = "{\"applyEndTime\":48,\"autoFinishedTime\":7,\"orderStatusType\":5}";
    /**
     * 订单售后规则 非默认规则的规则内容
     */
    public static final String AFTER_SALE_NON_DEFAULT_RULE = "[{\"applyEndTime\":48,\"autoFinishedTime\":7,\"deliveryType\":0,\"orderStatusType\":5},{\"applyEndTime\":48,\"autoFinishedTime\":7,\"deliveryType\":1,\"orderStatusType\":5}]";
    /**
     * 管理员名称
     */
    public static final String ADMIN_NICK_SUFFIX = "管理员";
    /**
     * 超级管理员角色
     */
    public static final String ADMIN_NAME = "超级管理员";
    /**
     * 供应商配送员
     */
    public static final String SUPPLIER_DISTRIBUTOR = "供应商配送员";
    /**
     * super_admin 超级管理员
     */
    public static final Byte ADMIN_FLAG = 1;
    /**
     * 非 super_admin
     */
    public static final Byte NOT_ADMIN_FLAG = 0;


    // *********** 新增品牌商城  end  *********** //

    // *********** 外单商城  start  *********** //
    /**
     * 外单默认客户经理
     */
    public static final String OUTER_ORDER_DEFAULT_MANAGER = "外单默认客户经理";

    /**
     * 外单默认版本(订销版)
     */
    public static final Integer OUTER_ORDER_SALE_VERSION = 1;
    // *********** 外单商城  end  *********** //
}
