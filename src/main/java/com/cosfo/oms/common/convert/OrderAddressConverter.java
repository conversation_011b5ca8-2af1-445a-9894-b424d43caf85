package com.cosfo.oms.common.convert;

import com.cosfo.ordercenter.client.resp.order.OrderAddressResp;
import com.cosfo.summerfarm.model.dto.order.OrderAddressVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface OrderAddressConverter {

    OrderAddressConverter INSTANCE = Mappers.getMapper(OrderAddressConverter.class);

    OrderAddressVO toVO(OrderAddressResp orderAddress);
}
