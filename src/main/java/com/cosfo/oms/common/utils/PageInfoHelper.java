package com.cosfo.oms.common.utils;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cofso.page.PageResp;
import com.cofso.preferential.client.resp.ProductSkuPreferentialPageResp;
import com.cosfo.oms.merchant.model.po.StoreAddressAuditLog;
import com.cosfo.oms.merchant.model.vo.StoreAddressAuditVO;
import com.cosfo.oms.product.model.vo.ProductPreferentialCostPriceVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import java.util.List;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 */
public class PageInfoHelper {
    public PageInfoHelper() {
    }

    public static <T> PageInfo<T> createPageInfo(List<T> data, Integer pageSize) {
        PageInfo pageInfo = new PageInfo(data);
        pageInfo.setPageSize(pageSize);
        pageInfo.setSize(pageInfo.getTotal() != 0 ? ((new Long(pageInfo.getTotal())).intValue() / pageSize) + 1 : 0);
        return pageInfo;
    }

    public static <T> PageInfo<T> createPageInfo(int pageIndex, int pageSize, Supplier<List<T>> query) {
        PageHelper.startPage(pageIndex, pageSize);
        return new PageInfo((List) query.get());
    }

    public static <T> PageInfo<T> toPageInfo(com.github.pagehelper.Page page, List<T> list) {
        PageInfo<T> pageInfo = new PageInfo<>();
        pageInfo.setPageNum(page.getPageNum());
        pageInfo.setPageSize(page.getPageSize());
        pageInfo.setPages(page.getPages());
        pageInfo.setTotal(page.getTotal());
        pageInfo.setList(list);
        return pageInfo;
    }

    public static <T, S> PageInfo<T> pageInfoCopy(PageInfo<S> sourcePageInfo, List<T> targetList) {
        PageInfo<T> respPageInfo = new PageInfo<>();
        respPageInfo.setPageNum(sourcePageInfo.getPageNum());
        respPageInfo.setPageSize(sourcePageInfo.getPageSize());
        respPageInfo.setSize(sourcePageInfo.getSize());
        respPageInfo.setStartRow(sourcePageInfo.getStartRow());
        respPageInfo.setEndRow(sourcePageInfo.getEndRow());
        respPageInfo.setPages(sourcePageInfo.getPages());
        respPageInfo.setPrePage(sourcePageInfo.getPrePage());
        respPageInfo.setNextPage(sourcePageInfo.getNextPage());
        respPageInfo.setIsFirstPage(sourcePageInfo.isIsFirstPage());
        respPageInfo.setIsLastPage(sourcePageInfo.isIsLastPage());
        respPageInfo.setHasPreviousPage(sourcePageInfo.isHasPreviousPage());
        respPageInfo.setHasNextPage(sourcePageInfo.isHasNextPage());
        respPageInfo.setNavigatePages(sourcePageInfo.getNavigatePages());
        respPageInfo.setNavigatepageNums(sourcePageInfo.getNavigatepageNums());
        respPageInfo.setNavigateFirstPage(sourcePageInfo.getNavigateFirstPage());
        respPageInfo.setNavigateLastPage(sourcePageInfo.getNavigateLastPage());
        respPageInfo.setTotal(sourcePageInfo.getTotal());
        respPageInfo.setList(targetList);
        return respPageInfo;
    }

    public static <T> PageInfo<T> mpPageToPageInfo(List<T> list, Page page) {
        PageInfo<T> pageInfo = new PageInfo<>();
        pageInfo.setPageNum((int) page.getCurrent());
        pageInfo.setPageSize((int) page.getSize());
        pageInfo.setPages((int) page.getPages());
        pageInfo.setTotal(page.getTotal());
        pageInfo.setList(list);
        return pageInfo;
    }

    public static <T> PageInfo<T> itemPageToPageInfo(List<T> list, PageResp page) {
        PageInfo<T> pageInfo = new PageInfo<>();
        pageInfo.setPageNum(page.getPageNum());
        pageInfo.setPageSize(page.getPageSize());
        pageInfo.setPages(page.getPages());
        pageInfo.setTotal(page.getTotal());
        pageInfo.setList(list);
        return pageInfo;
    }
}
