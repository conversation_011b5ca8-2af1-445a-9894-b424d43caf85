package com.cosfo.oms.common.utils;

import com.cosfo.oms.common.constant.NumberConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.regex.Pattern;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/5/31
 */
@Slf4j
public class FormatUtils {
    /**
     * 用于产生单号的日期格式化对象
     */
    public static DateTimeFormatter smallDateFormatter = DateTimeFormatter.ofPattern("yyMMdd");

    /**
     * 格式化全日期和时间的对象
     */
    public static DateTimeFormatter fullDateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 仅格式化日期的对象
     */
    public static DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    /**
     * 小时分钟格式
     */
    public static SimpleDateFormat hourTimeFormatter = new SimpleDateFormat("HH:mm");

    /**
     * 手机号格式
     */
    public static Pattern phonePattern = Pattern.compile("^[1]\\d{10}$");

    /**
     * 身份证号格式
     */
    public static Pattern identityNumberPattern = Pattern.compile("^[1-9]\\d{5}[1-9]\\d{3}((0[1-9])|(1[0-2]))(0[1-9]|([1|2][0-9])|3[0-1])((\\d{4})|\\d{3}X)$");

    /**
     * 自动将对象的字符串属性的字段进行首位空格去除
     *
     * @param o 要处理的对象
     */
    public static void trimWhitespace(Object o) {
        if (o == null) {
            return;
        }

        Class<?> clazz = o.getClass();
        for (Field field : clazz.getDeclaredFields()) {
            if (field.getType() == String.class) {
                boolean accessible = field.isAccessible();
                field.setAccessible(true);
                try {
                    field.set(o, StringUtils.trimWhitespace((String) field.get(o)));
                } catch (Exception ex) {

                }

                field.setAccessible(accessible);
            }
        }
    }

    /**
     * 将对象所有String类型的字段trimToNull
     *
     * @param o
     */
    public static void trimFieldToNull(Object o) {
        if (o == null) {
            return;
        }

        Class<?> clazz = o.getClass();
        for (Field field : clazz.getDeclaredFields()) {
            if (field.getType() == String.class) {
                boolean accessible = field.isAccessible();
                field.setAccessible(true);
                try {
                    String value = trimToNull((String) field.get(o));
                    field.set(o, value);
                } catch (Exception ex) {

                }

                field.setAccessible(accessible);
            }
        }
    }

    /**
     * 将字符串
     *
     * @param str
     * @return 非null的一个首位不含空格字符串
     */
    public static String trimToEmpty(String str) {
        return str == null ? "" : StringUtils.trimWhitespace(str);
    }

    /**
     * 将一个字符串首尾空格去掉，如果为空，返回null
     *
     * @param val 待处理的字符串
     * @return
     */
    public static String trimToNull(String val) {
        val = StringUtils.trimWhitespace(val);
        if (val == null || val.isEmpty()) {
            val = null;
        }

        return val;
    }

    /**
     * 构造模糊匹配的字符串
     *
     * @param value
     * @return
     */
    public static String makeFuzzySearchTerm(String value) {
        value = FormatUtils.trimToNull(value);
        if (value == null) {
            return null;
        }

        return '%' + value + '%';
    }

    /**
     * 产生以prefix为前缀的单号。序数部分采用纳秒生成，保证长度固定
     * 格式为：前缀 + 日期 + 16位十进制数字纳秒序号
     *
     * @param prefix 单号前缀
     * @return 产生一个包含16位纳秒序号的单号字符串
     */
    public static String generateCode(String prefix) {
        String nsTimeString = String.format("%016d", System.nanoTime());
        if (nsTimeString.length() > NumberConstants.SIXTEEN) {
            // 右截断
            nsTimeString = nsTimeString.substring(nsTimeString.length() - NumberConstants.SIXTEEN);
        }

        LocalDateTime date = LocalDateTime.now();
        return String.format("%s%s%s", prefix, date.format(smallDateFormatter), nsTimeString);
    }

    /**
     * 产生以单号为前缀，序号为后缀的明细单号，保证长度固定
     *
     * @param code  单号
     * @param index 序号
     * @return
     */
    public static String generateItemCode(String code, int index) {
        Assert.isTrue(index < 10000, "序号不能超过9999");
        return String.format("%s%04d", code, index);
    }

    public static String formatDate(Date date, DateTimeFormatter formatter) {
        if (date == null) {
            return null;
        }

        Instant instant = date.toInstant();
        ZoneId zoneId = ZoneId.systemDefault();
        LocalDateTime localDateTime = instant.atZone(zoneId).toLocalDateTime();
        return localDateTime.format(formatter);
    }

    /**
     * 格式化成全日期时间格式：yyyy-MM-dd HH:mm:ss
     *
     * @param date 日期对象
     * @return 格式化字符串
     */
    public static String formatFullDate(Date date) {
        return formatDate(date, fullDateTimeFormatter);
    }

    /**
     * 格式化成日期时间格式：yyyy-MM-dd
     *
     * @param date 日期对象
     * @return 格式化字符串
     */
    public static String formatDate(Date date) {
        return formatDate(date, dateFormatter);
    }

    /**
     * 解析日期字符串，线程安全
     *
     * @param dateString 格式为yyyy-MM-dd的字符串
     * @return 日期对象
     */
    public static Date parseDate(String dateString) {
        if (StringUtils.isEmpty(dateString)) {
            return null;
        }

        LocalDate localDate = LocalDate.parse(dateString, dateFormatter);
        ZoneId zoneId = ZoneId.systemDefault();
        ZonedDateTime zdt = localDate.atStartOfDay(zoneId);
        return Date.from(zdt.toInstant());
    }

    /**
     * 解析日期时间字符串，线程安全
     *
     * @param dateTimeString 格式为yyyy-MM-dd HH:mm:ss的字符串
     * @return
     */
    public static Date parseDateTime(String dateTimeString) {
        if (StringUtils.isEmpty(dateTimeString)) {
            return null;
        }

        LocalDateTime localDateTime = LocalDateTime.parse(dateTimeString, fullDateTimeFormatter);
        ZoneId zoneId = ZoneId.systemDefault();
        ZonedDateTime zdt = localDateTime.atZone(zoneId);
        return Date.from(zdt.toInstant());
    }

    /**
     * 将日期格式2019-01-01，格式化成第二天的日期格式：2019-01-02，用于时间范围的过滤
     *
     * @param dateString
     * @return
     */
    public static String formatEndDate(String dateString) {
        if (StringUtils.isEmpty(dateString)) {
            return null;
        }

        LocalDate localDate = LocalDate.parse(dateString, dateFormatter);
        ZoneId zoneId = ZoneId.systemDefault();
        ZonedDateTime zdt = localDate.atStartOfDay(zoneId);
        zdt = zdt.plusDays(1);
        return formatDate(Date.from(zdt.toInstant()), dateFormatter);
    }

    public static Date formatEndDate(Date date) {
        if (date == null) {
            return null;
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    public static Date formatBeginDate(Date date) {
        if (date == null) {
            return null;
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 转值为字符串
     *
     * @param value 对象
     * @return 字符串
     */
    public static String toString(Object value) {
        if (value == null) {
            return "";
        } else if (value instanceof Boolean) {
            if (value.equals(true)) {
                return "1";
            } else {
                return "0";
            }
        }

        return value.toString();
    }

    /**
     * 解析日期时间字符
     */
    public static Date parseDateTime(String dateTimeString, SimpleDateFormat formatter) {
        if (StringUtils.isEmpty(dateTimeString)) {
            return null;
        }

        try {
            return formatter.parse(dateTimeString);
        } catch (ParseException e) {
            log.warn("解析日期失败，msg = {}", e.getMessage(), e);
        }

        return null;
    }

    /**
     * 转换成 Calendar
     *
     * @param dateTimeString
     * @param formatter
     * @return
     */
    public static Calendar getCronCalendar(String dateTimeString, SimpleDateFormat formatter) {
        Date date = FormatUtils.parseDateTime(dateTimeString, formatter);

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        return calendar;
    }

    public static boolean isEmpty(String str) {
        if (str == null) {
            return true;
        }

        return str.isEmpty();
    }

    /**
     * 功能：驼峰命名转下划线命名
     * 小写和大写紧挨一起的地方,加上分隔符,然后全部转小写
     */
    public static String camel2under(String c) {
        String separator = "_";
        c = c.replaceAll("([a-z])([A-Z])", "$1" + separator + "$2").toLowerCase();
        return c;
    }

    /**
     * 功能：下划线命名转驼峰命名
     * 将下划线替换为空格,将字符串根据空格分割成数组,再将每个单词首字母大写
     *
     * @param s
     * @return
     */
    public static String under2camel(String s) {
        String separator = "_";
        String under = "";
        s = s.toLowerCase().replace(separator, " ");
        String sarr[] = s.split(" ");
        for (int i = 0; i < sarr.length; i++) {
            if (i != 0) {
                String w = sarr[i].substring(0, 1).toUpperCase() + sarr[i].substring(1);
                under += w;
            } else {
                under += sarr[i];
            }
        }

        return under;
    }

    public static String pad(String val, int len) {

        StringBuffer str = new StringBuffer();
        if (val != null) {
            str.append(val);
        } else {
            val = "";
        }

        if (str.length() > len) {
            return str.substring(0, len);
        }

        for (int i = val.length(); i < len; i++) {
            str.insert(0, '0');
        }
        return str.toString();
    }

    public static String formatIp(String ip) {
        //将长IP格式转为15位长格式。如**********转为 ***************
        if (ip == null || ip.length() == 0) {
            return "";
        }

        String[] parts = ip.split("\\.");
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < parts.length; i++) {
            if (sb.length() != 0) {
                sb.append('.');
            }

            sb.append(pad(parts[i], 3));
        }

        return sb.toString();
    }

    public static String simpleIp(String ip) {
        //将长IP格式的转为短格式的。如***************转为**********
        String[] parts = ip.split("\\.");
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < parts.length; i++) {
            if (sb.length() != 0) {
                sb.append('.');
            }

            sb.append(Integer.toString(Integer.parseInt(parts[i], 10)));
        }

        return sb.toString();
    }

    /**
     * 校验手机号, 1字头＋10位数字即可.
     *
     * @param phone 手机号
     * @return 手机号是否正确
     */
    public static boolean validatePhone(String phone) {
        return phonePattern.matcher(phone).matches();
    }

    /**
     * 校验身份证号
     *
     * @param id 身份证号
     * @return 身份证号是否正确
     */
    public static boolean validateIdentityNumber(String id) {
        return identityNumberPattern.matcher(id).matches();
    }

}
