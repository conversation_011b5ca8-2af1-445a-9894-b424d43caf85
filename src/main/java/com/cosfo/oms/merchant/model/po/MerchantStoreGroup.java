package com.cosfo.oms.merchant.model.po;

import java.time.LocalDateTime;
import java.io.Serializable;

import lombok.Data;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * 门店分组(MerchantStoreGroup)实体类
 *
 * <AUTHOR>
 * @since 2023-03-31 14:55:23
 */
@Data
@TableName("merchant_store_group")
public class MerchantStoreGroup implements Serializable {
    private static final long serialVersionUID = -20897069589514648L;
    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 租户Id
     */
    private Long tenantId;
    /**
     * 分组名称
     */
    private String name;
    /**
     * create time
     */
    private LocalDateTime createTime;
    /**
     * update time
     */
    private LocalDateTime updateTime;
    /**
     * 0、非默认分组 1、默认分组
     */
    private Integer type;

}

