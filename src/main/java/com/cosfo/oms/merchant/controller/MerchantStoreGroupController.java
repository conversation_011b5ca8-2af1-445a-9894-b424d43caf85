package com.cosfo.oms.merchant.controller;

import com.cosfo.oms.controller.BaseController;
import com.cosfo.oms.merchant.model.vo.MerchantStoreGroupVO;
import com.cosfo.oms.merchant.service.MerchantStoreGroupService;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 门店分组
 *
 * @author: fss
 * @创建时间:
 */
@RestController
@RequestMapping(value = "/merchant/store/group")
public class MerchantStoreGroupController extends BaseController {

    @Resource
    private MerchantStoreGroupService merchantStoreGroupService;

    /**
     * 查询所有
     *
     * @return
     */
    @PostMapping("/query/list-all/{tenantId}")
    public CommonResult<List<MerchantStoreGroupVO>> listAll(@PathVariable Long tenantId) {
        return merchantStoreGroupService.listAll(tenantId);
    }

}
