package com.cosfo.oms.msgscene.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date : 2023/2/10 18:08
 * 帆台场景详情
 *
 */
@Data
public class MsgSceneDetailVO implements Serializable {

    private static final long serialVersionUID = -3452798143394807784L;

    /**
     * primary key
     * 场景ID
     */
    private Long id;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 模版池id
     * 关联帆台模板ID
     */
    private Long templateId;

    /**
     * 模版类型0=微信
     */
    private Integer templateType;

    /**
     * 模版名称
     * 关联帆台模板标题
     */
    private String templateName;

    /**
     * 关键字
     */
    private String keywords;

    /**
     * 场景名称
     */
    private String sceneName;

    /**
     * 字段数
     */
    private Integer sceneCount;

    /**
     * 描述（场景说明）
     */
    private String description;

    /**
     * 场景 状态0=不可用（禁用）;1=可用(启用中)
     */
    private Integer sceneStatus;

    /**
     * 消息详情url（路径）
     */
    private String page;

    /**
     * 操作人
     */
    private Long updater;


    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
