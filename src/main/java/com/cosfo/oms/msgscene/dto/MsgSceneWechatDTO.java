package com.cosfo.oms.msgscene.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date : 2023/2/10 15:03
 * 帆台场景列表查询入参、场景更新入参
 */
@Data
public class MsgSceneWechatDTO implements Serializable {

    private static final long serialVersionUID = -5122290134890902815L;

    /**
     * primary key（场景id）
     */
    private Long id;

    /**
     * 场景名称
     */
    private String sceneName;

    /**
     * 是否关联模板
     * 0未关联 1关联
     */
    private Integer privateFlag;

    /**
     * 帆台维度的启用、禁用
     * 场景 状态0=不可用（禁用）;1=可用（启用中）
     */
    private Integer sceneStatus;

    /**
     * 当前页码
     */
    private Integer pageIndex;

    /**
     * 页面大小
     */
    private Integer pageSize;

    /**
     * 更新场景时使用
     * 消息详情url（路径）
     */
    private String page;

    /**
     * 更新时使用
     * 更新操作人
     */
    private Long updater;
}
