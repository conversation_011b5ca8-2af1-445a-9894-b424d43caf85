package com.cosfo.oms.market.service.impl;

import com.alibaba.excel.util.StringUtils;
import com.cosfo.oms.common.context.GoodsTypeEnum;
import com.cosfo.oms.common.context.WarehouseTypeEnum;
import com.cosfo.oms.market.model.po.Market;
import com.cosfo.oms.market.model.po.MarketClassification;
import com.cosfo.oms.market.model.vo.MarketSpuVO;
import com.cosfo.oms.market.repository.MarketClassificationRepository;
import com.cosfo.oms.market.repository.MarketRepository;
import com.cosfo.oms.market.service.MarketService;
import com.cosfo.oms.product.model.dto.ProductCategoryDTO;
import com.cosfo.oms.product.service.ProductCategoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Author: fansongsong
 * @Date: 2023-04-13
 * @Description:
 */
@Slf4j
@Service
public class MarketServiceImpl implements MarketService {

    @Resource
    private MarketRepository marketRepository;

    @Resource
    private MarketClassificationRepository marketClassificationRepository;

    @Resource
    private ProductCategoryService productCategoryService;

    @Override
    public MarketSpuVO detail(Long id) {
        Market market = marketRepository.getById(id);
        // 查询分类
        MarketClassification marketClassification = marketClassificationRepository.queryByMarketId(market.getId(), market.getTenantId());

        return assemblyMarketSpuVo(market, marketClassification);
    }

    @Override
    public String getOperationMode(Integer warehouseType, Integer GoodsType) {
        if(WarehouseTypeEnum.NO_WAREHOUSE.getCode().equals(warehouseType) ||  WarehouseTypeEnum.PROPRIETARY.getCode().equals(warehouseType) ){
            return "自营货品-自营仓";
        }else if(WarehouseTypeEnum.THREE_PARTIES.getCode().equals(warehouseType) && GoodsTypeEnum.SELF_GOOD_TYPE.getCode().equals(GoodsType)){
            return "自营货品-代仓";
        }else if(WarehouseTypeEnum.THREE_PARTIES.getCode().equals(warehouseType) && GoodsTypeEnum.QUOTATION_TYPE.getCode().equals(GoodsType)){
            return "报价货品";
        }

        return StringUtils.EMPTY;
    }

    private MarketSpuVO assemblyMarketSpuVo(Market market, MarketClassification marketClassification) {
        MarketSpuVO marketSpuVO = new MarketSpuVO();
        marketSpuVO.setId(market.getId());
        marketSpuVO.setTitle(market.getTitle());
        marketSpuVO.setSubTitle(market.getSubTitle());
        marketSpuVO.setMainPicture(market.getMainPicture());
        marketSpuVO.setDetailPicture(market.getDetailPicture());
        if (!Objects.isNull(marketClassification)) {
            marketSpuVO.setSecondClassificationId(marketClassification.getId());
            marketSpuVO.setSecondClassificationName(marketClassification.getName());
            MarketClassification firstMarketClassification = marketClassificationRepository.getById(marketClassification.getParentId());
            if(!Objects.isNull(firstMarketClassification)) {
                marketSpuVO.setFirstClassificationId(firstMarketClassification.getId());
                marketSpuVO.setFirstClassificationName(firstMarketClassification.getName());
            }
        }

        if(Objects.nonNull(market.getCategoryId())) {
            ProductCategoryDTO productCategoryDTO = productCategoryService.selectWholeCategory(market.getCategoryId());
            marketSpuVO.setFirstCategoryId(productCategoryDTO.getFirstCategoryId());
            marketSpuVO.setSecondCategoryId(productCategoryDTO.getSecondCategoryId());
            marketSpuVO.setThirdCategoryId(productCategoryDTO.getThirdCategoryId());
            marketSpuVO.setFirstCategory(productCategoryDTO.getFirstCategoryName());
            marketSpuVO.setSecondCategory(productCategoryDTO.getSecondCategoryName());
            marketSpuVO.setThirdCategory(productCategoryDTO.getThirdCategoryName());
        }

        return marketSpuVO;
    }
}
