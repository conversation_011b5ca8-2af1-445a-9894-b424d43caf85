package com.cosfo.oms.market.mapper;

import com.cosfo.oms.market.model.dto.MarketAreaItemDTO;
import com.cosfo.oms.market.model.po.MarketAreaItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;

@Mapper
public interface MarketAreaItemMapper {
    int deleteByPrimaryKey(Long id);

    int insert(MarketAreaItem record);

    int insertSelective(MarketAreaItem record);

    MarketAreaItem selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(MarketAreaItem record);

    int updateByPrimaryKey(MarketAreaItem record);

    /**
     * 下架商品
     *
     * @param skuId
     * @return
     */
    int soldOutBySkuId(@Param("skuId") Long skuId);

    /**
     * 查询商品上架信息
     *
     * @param skuId
     * @return
     */
    MarketAreaItemDTO selectMarketAreaItemBySkuId(@Param("skuId") Long skuId);

    /**
     * 更新商城售卖价
     *
     * @param id
     * @param price
     */
    void updateMarketAreaItemPrice(@Param("id") Long id,
                                   @Param("price") BigDecimal price);

    /**
     * 根据租户Id和销售商品Id查询
     *
     * @param tenantId
     * @param itemId
     * @return
     */
    MarketAreaItem selectByTenantIdAndItemId(@Param("tenantId") Long tenantId,@Param("itemId") Long itemId);
}