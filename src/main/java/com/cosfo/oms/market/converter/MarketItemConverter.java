package com.cosfo.oms.market.converter;


import com.cosfo.oms.market.model.dto.MarketItemDTO;
import com.cosfo.oms.market.model.po.MarketAreaItem;
import com.cosfo.oms.market.model.po.MarketItem;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Objects;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/12/16
 */
public class MarketItemConverter {

    /**
     * 转化为MarketItemDTO
     *
     * @param marketItem
     * @return
     */
    public static MarketItemDTO convertToMarketItemDTO(MarketItem marketItem) {
        if (marketItem == null) {
            return null;
        }

        MarketItemDTO marketItemDTO = new MarketItemDTO();
        marketItemDTO.setId(marketItem.getId());
        marketItemDTO.setMarketId(marketItem.getMarketId());
        marketItemDTO.setTenantId(marketItem.getTenantId());
        marketItemDTO.setSkuId(marketItem.getSkuId());
        marketItemDTO.setSpecification(marketItem.getSpecification());
        marketItemDTO.setWeightNotes(marketItem.getWeightNotes());
        marketItemDTO.setSpecificationUnit(marketItem.getSpecificationUnit());
        marketItemDTO.setBrandName(marketItem.getBrandName());
        marketItemDTO.setSupplierId(marketItem.getSupplierId());
        marketItemDTO.setSupplierName(marketItem.getSupplierName());
        marketItemDTO.setItemCode(marketItem.getItemCode());
        marketItemDTO.setMaxAfterSaleAmount(marketItem.getMaxAfterSaleAmount());
        marketItemDTO.setAfterSaleUnit(marketItem.getAfterSaleUnit());
        marketItemDTO.setGoodsType(marketItem.getGoodsType());
        return marketItemDTO;
    }
}
