package com.cosfo.oms.system.service.Impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cosfo.oms.common.constant.Constants;
import com.cosfo.oms.system.mapper.SystemParametersMapper;
import com.cosfo.oms.system.mapper.SystemWechantTpInfoMapper;
import com.cosfo.oms.system.model.po.SystemParameters;
import com.cosfo.oms.system.model.po.SystemWechantTpInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Scope("singleton")
@Service
@Slf4j
public class DictSysParameter {

    @Resource
    SystemParametersMapper sysParameterMapper;

    private Map<String, SystemParameters> parameterMap = new HashMap<String,SystemParameters>();
    @Resource
    private SystemWechantTpInfoMapper systemWechantTpInfoMapper;
    @Value("${tp.appId}")
    private String appId;
    private SystemWechantTpInfo systemWechantTpInfo;

    @PostConstruct
    public SystemWechantTpInfo init() {
        systemWechantTpInfo = systemWechantTpInfoMapper.selectOne(new QueryWrapper<SystemWechantTpInfo>().eq(Constants.APPID,appId));
        List<SystemParameters> list = sysParameterMapper.selectAll();
        for (SystemParameters sysParameter:list) {
            parameterMap.put(sysParameter.getParamKey(), sysParameter);
        }
        return systemWechantTpInfo;
    }

    public SystemWechantTpInfo getParameter() {
        if (ObjectUtils.isEmpty(systemWechantTpInfo)){
            init();
        }
        return systemWechantTpInfo;
    }

    public String getParameter(String key, String default_value) {
        SystemParameters sysParameter = parameterMap.get(key);
        if(null == sysParameter || null == sysParameter.getParamValue() || 0 == sysParameter.getParamValue().length()) {
            return default_value;
        } else {
            return sysParameter.getParamValue();
        }
    }

    public Integer getParameter(String key, Integer default_value) {
        SystemParameters sysParameter = parameterMap.get(key);
        if(null == sysParameter || null == sysParameter.getParamValue() || 0 == sysParameter.getParamValue().length()) {
            return default_value;
        } else {
            return Integer.valueOf(sysParameter.getParamValue());
        }
    }

    public void refresh() {
        log.debug("reload system parameters");
        init();
    }
}
