package com.cosfo.oms.testcodde.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.cosfo.oms.testcodde.model.Testcode;
import com.cosfo.oms.testcodde.mapper.TestcodeMapper;
import com.cosfo.oms.testcodde.dao.TestcodeDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 品牌表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-29
 */
@Service
public class TestcodeDaoImpl extends ServiceImpl<TestcodeMapper, Testcode> implements TestcodeDao {

    @Override
    public List<Testcode> queryByCondition(String name) {
        //查询
        LocalDateTime beginTime = null;
        LambdaQueryWrapper<Testcode> wrapper = new LambdaQueryWrapper();
        wrapper.like(ObjectUtils.isNotEmpty(name),Testcode::getName,name)//like
                .eq(ObjectUtils.isNotEmpty(name),Testcode::getName,name)//等于
                .ge(ObjectUtils.isNotEmpty(beginTime),Testcode::getCreateTime,beginTime)//大于等于
                .le(ObjectUtils.isNotEmpty(beginTime),Testcode::getCreateTime,beginTime)//小于等于
                .in(ObjectUtils.isNotEmpty(null),Testcode::getId, Arrays.asList(1,2,3))//in
                .orderByDesc(Testcode::getCreateTime)//倒叙
                .orderByAsc(Testcode::getCreateTime);//正叙
        List<Testcode> list = list(wrapper);
        Testcode one = getOne(wrapper);
//        IPage<Testcode> page = page(wrapper);//没有用这个分页插件。。。

        //修改
        LambdaUpdateWrapper<Testcode> lambdaUpdateWrapper = new LambdaUpdateWrapper();
        lambdaUpdateWrapper.set(Testcode::getName,name);//设置值
        lambdaUpdateWrapper.eq(Testcode::getId,1);//条件
        update(lambdaUpdateWrapper);

        //查询 某个字段
        LambdaQueryChainWrapper<Testcode> wrapper1 = new LambdaQueryChainWrapper<>(getBaseMapper())
                .select(Testcode::getId, Testcode::getName, Testcode::getCreateTime)
                .eq(Testcode::getId, 1);
        List<Testcode> list1 = list(wrapper);
        return null;
    }
}
