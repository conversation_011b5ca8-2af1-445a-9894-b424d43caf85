package com.cosfo.oms.order.service;

import com.cosfo.oms.model.dto.LoginContextInfoDTO;
import com.cosfo.oms.order.model.dto.OrderAfterSaleAuditDTO;
import com.cosfo.oms.order.model.dto.OrderAfterSaleBizDTO;
import com.cosfo.oms.order.model.dto.OrderAfterSaleQueryDTO;
import com.cosfo.oms.order.model.dto.OrderAfterSaleUnAuditModifyQuantityDTO;
import com.cosfo.oms.order.model.vo.aftersale.OrderAfterSaleInfoVO;
import com.cosfo.oms.order.model.vo.aftersale.OrderAfterSaleProductVO;
import com.github.pagehelper.PageInfo;
import net.xianmu.common.result.CommonResult;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/8/25 18:24
 */
public interface OrderAfterSaleDomainService {

    /**
     * 售后订单列表
     *
     * @param orderAfterSaleQueryDTO
     * @param loginContextInfoDTO
     * @return
     */
    CommonResult<PageInfo<OrderAfterSaleInfoVO>> getOrderListAfterSale(OrderAfterSaleQueryDTO orderAfterSaleQueryDTO, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 导出售后订单列表
     * @param afterSaleQueryDTO
     * @param loginContextInfoDTO
     */
    void exportOrdersAfterSale(OrderAfterSaleQueryDTO afterSaleQueryDTO, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 售后订单详情
     *
     * @param orderId
     * @return
     */
    CommonResult getOrderDetails(Long orderId);

    /**
     * 售后订单详情售后商品列表
     *
     * @param orderId
     * @return
     */
    CommonResult<List<OrderAfterSaleProductVO>> getOrderAfterSaleCommodity(Long orderId);

    /**
     * 售后订单详情原订单列表
     *
     * @param orderId
     * @return
     */
    CommonResult getOrderCommodity(Long orderId);


    /**
     * 审核
     *
     * @param orderAfterSaleAuditDTO
     * @param merchantInfoDTO
     * @return
     */
    CommonResult reviewSubmissions(OrderAfterSaleAuditDTO orderAfterSaleAuditDTO, LoginContextInfoDTO merchantInfoDTO);

    /**
     * 售后待审核订单修改数量
     * @param orderAfterSaleUnAuditModifyQuantityDTO
     * @param loginContextInfoDTO
     * @return
     */
    CommonResult modifyQuantity(OrderAfterSaleUnAuditModifyQuantityDTO orderAfterSaleUnAuditModifyQuantityDTO, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 取消售后单
     *
     * @param orderAfterSaleDTO
     * @param merchantInfoDTO
     * @return
     */
    CommonResult cancel(OrderAfterSaleBizDTO orderAfterSaleDTO, LoginContextInfoDTO merchantInfoDTO);

    /**
     * 发起退款
     *
     * @param orderAfterSaleDTO
     * @param loginContextInfoDTO
     * @return
     */
    CommonResult refund(OrderAfterSaleBizDTO orderAfterSaleDTO, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 校验可售后的金额的数量
     *
     * @param id
     * @param applyPrice
     * @param applyAmount
     */
    void verifyApplyPriceAndAmount(Long id, BigDecimal applyPrice, Integer applyAmount);

    /**
     * 管理员发起售后
     *
     * @param orderAfterSaleDTO
     * @param loginContextInfoDTO
     * @return
     */
    CommonResult<Long> save(OrderAfterSaleBizDTO orderAfterSaleDTO, LoginContextInfoDTO loginContextInfoDTO);

    /**
     * 计算售后金额
     * @param orderItemId
     * @param amount
     * @return
     */
    BigDecimal calculateRefundPrice(Long orderItemId, Integer amount);

    /**
     * 服务商确认
     * @param orderAfterSaleAuditDTO
     */
    Boolean serviceProviderReviewSubmissions(OrderAfterSaleAuditDTO orderAfterSaleAuditDTO);
}
