package com.cosfo.oms.order.model.vo;

import com.cosfo.oms.order.model.dto.OrderSelfLiftingDTO;
import com.cosfo.summerfarm.model.dto.order.OrderAddressVO;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 描述:
 *
 * @author: Song
 * @创建时间: 2022/5/16
 */
@Data
public class OrderVO {
    /**
     * 订单编号
     */
    private Long orderId;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 门店类型
     */
    private Integer storeType;
    /**
     * 联系方式
     */
    private String contactPhone;
    /**
     * 配送仓类型
     */
    private Integer warehouseType;
    /**
     * 供应商租户Id
     */
    private Long supplierTenantId;
    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 商品总额
     */
    private BigDecimal productTotalPrice;
    /**
     * 应付金额
     */
    private BigDecimal payablePrice;
    /**
     * 配送费
     */
    private BigDecimal deliveryFee;
    /**
     * 总金额
     */
    private BigDecimal totalPrice;
    /**
     * 订单创建时间
     */
    private LocalDateTime orderTime;
    /**
     * 账号Id
     */
    private Long accountId;
    /**
     * 下单账号
     */
    private String accountName;
    /**
     * 配送时间
     */
    private LocalDateTime deliveryTime;
    /**
     * 支付方式
     */
    private Integer payType;
    /**
     * 支付时间
     */
    private LocalDateTime payTime;
    /**
     * 总件数
     */
    private Integer totalAmount;
    /**
     * 收获地址信息
     */
    private OrderAddressVO orderAddressVO;
    /**
     * 门店名称
     */
    private String storeName;
    /**
     * 门店ID
     */
    private Long storeId;
    /**
     * 门店编号
     */
    private String storeNo;
    /**
     * 订单项
     */
    private List<OrderItemVO> orderItemVOS;
    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 租户名称
     */
    private String tenantName;
    /**
     * 自提信息
     */
    private List<OrderSelfLiftingDTO> orderSelfLiftingDTOS;

    /**
     * 订单完成时间
     */
    private LocalDateTime finishedTime;

    /**
     * 订单备注
     */
    private String remark;
    /**
     * 可申请售后时效
     */
    private Integer applyEndTime;
    /**
     * 自动完成时间
     */
    private Integer autoFinishedTime;
    /**
     * 订单类型 0-普通订单,1=组合订单, 2=预售订单
     */
    private Integer orderType;

    /**
     * 外部系统订单号
     */
    private String customerOrderId;

    /**
     * 履约类型，0：城配履约，1：快递履约
     */
    private Integer fulfillmentType;
}
