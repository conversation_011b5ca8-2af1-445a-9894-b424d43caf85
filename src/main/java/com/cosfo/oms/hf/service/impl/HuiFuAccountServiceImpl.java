package com.cosfo.oms.hf.service.impl;

import com.cosfo.oms.common.context.AccountTypeEnum;
import com.cosfo.oms.common.context.ProfitSharingSwitchEnum;
import com.cosfo.oms.common.utils.StringUtils;
import com.cosfo.oms.hf.dao.HuiFuAccountDao;
import com.cosfo.oms.hf.model.dto.HuiFuAccountDTO;
import com.cosfo.oms.hf.model.po.HuiFuAccount;
import com.cosfo.oms.hf.service.HuiFuAccountService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.ParamsException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @description:
 * @author: George
 * @date: 2025-06-19
 **/
@Slf4j
@Service
public class HuiFuAccountServiceImpl implements HuiFuAccountService {

    @Resource
    private HuiFuAccountDao huiFuAccountDao;

    @Override
    public Long addHfAccount(HuiFuAccountDTO huiFuAccountDTO) {
        validateHuiFuAccount(huiFuAccountDTO);
        HuiFuAccount account = buildHfAccount(huiFuAccountDTO);
        huiFuAccountDao.save(account);

        Long id = account.getId();
        account.setAccountId(id);
        huiFuAccountDao.updateById(account);
        log.info("新增汇付账户成功，accountId:{}", id);
        return id;
    }

    private void validateHuiFuAccount(HuiFuAccountDTO huiFuAccountDTO) {
        if (huiFuAccountDTO == null) {
            throw new ParamsException("参数不能为空");
        }
        if (StringUtils.isEmpty(huiFuAccountDTO.getName())) {
            throw new ParamsException("名称不能为空");
        }
        if (StringUtils.isEmpty(huiFuAccountDTO.getHuiFuId())) {
            throw new ParamsException("汇付商户id不能为空");
        }
    }

    private HuiFuAccount buildHfAccount(HuiFuAccountDTO huiFuAccountDTO) {
        HuiFuAccount account = new HuiFuAccount();
        account.setHuifuId(huiFuAccountDTO.getHuiFuId());
//        account.setAccountId();
        account.setAccountType(AccountTypeEnum.NORMAL_RECEIVER.getType());
        account.setSharingSwitch(ProfitSharingSwitchEnum.OPEN.getCode());
        account.setTenantId(huiFuAccountDTO.getTenantId());
        account.setRegName(huiFuAccountDTO.getName());
        return account;
    }
}
