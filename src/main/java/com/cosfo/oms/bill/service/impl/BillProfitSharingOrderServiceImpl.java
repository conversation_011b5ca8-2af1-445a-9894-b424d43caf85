package com.cosfo.oms.bill.service.impl;

import com.cofso.item.client.enums.GoodsTypeEnum;
import com.cosfo.oms.bill.mapper.BillProfitSharingOrderMapper;
import com.cosfo.oms.bill.mapper.BillProfitSharingSnapshotMapper;
import com.cosfo.oms.bill.model.po.BillProfitSharingOrder;
import com.cosfo.oms.bill.model.po.BillProfitSharingSnapshot;
import com.cosfo.oms.bill.service.BillProfitSharingOrderService;
import com.cosfo.oms.common.context.ProfitSharingDeliveryTypeEnums;
import com.cosfo.oms.facade.order.OrderItemSnapshotQueryFacade;
import com.cosfo.ordercenter.client.resp.order.OrderItemSnapshotResp;
import net.xianmu.common.exception.ProviderException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @description:
 * @author: George
 * @date: 2024-01-26
 **/
@Service
public class BillProfitSharingOrderServiceImpl implements BillProfitSharingOrderService {

    @Resource
    private BillProfitSharingSnapshotMapper billProfitSharingSnapshotMapper;
    @Resource
    private BillProfitSharingOrderMapper billProfitSharingOrderMapper;
    @Resource
    private OrderItemSnapshotQueryFacade orderItemSnapshotQueryFacade;

    @Override
    public BillProfitSharingOrder getBillProfitSharingOrder(Long tenantId, Long orderId, Long orderItemId) {
        List<BillProfitSharingSnapshot> billProfitSharingSnapshots = billProfitSharingSnapshotMapper.queryByTenantIdAndOrderId(tenantId, orderId);
        if (org.springframework.util.CollectionUtils.isEmpty(billProfitSharingSnapshots)) {
            return null;
        }
        BillProfitSharingSnapshot snapshot = billProfitSharingSnapshots.get(0);
        Integer deliveryType = snapshot.getDeliveryType();
        Long supplierId = null;
        if (Objects.equals(deliveryType, ProfitSharingDeliveryTypeEnums.NO_WAREHOUSE.getType())) {
//            OrderItemSnapshotDTO orderItemSnapshotDTO = RpcResultUtil.handle(orderItemSnapshotQueryService.queryByOrderItemId(orderItemId));
            OrderItemSnapshotResp orderItemSnapshotResp = orderItemSnapshotQueryFacade.queryByOrderItemId(orderItemId);
            if (Objects.isNull(orderItemSnapshotResp)) {
                throw new ProviderException("未查询到订单项快照信息");
            }
            Integer goodsType = orderItemSnapshotResp.getGoodsType();
            supplierId = Objects.equals(goodsType, GoodsTypeEnum.NO_GOOD_TYPE.getCode()) ? orderItemSnapshotResp.getSupplierTenantId() : null;
        }
        return billProfitSharingOrderMapper.queryByTenantAndOrderAndSupplierId(tenantId, orderId, supplierId);
    }
}
