package com.cosfo.oms.bill.model.vo;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@ToString
public class PrepaymentTransactionTotalVO implements Serializable {

    /**
     * 收入
     */
    private BigDecimal income;

    /**
     * 支出
     */
    private BigDecimal expenses;

    public PrepaymentTransactionTotalVO() {
        this.income = BigDecimal.ZERO;
        this.expenses = BigDecimal.ZERO;
    }
}
